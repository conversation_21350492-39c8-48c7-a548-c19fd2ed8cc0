import React, { useState } from 'react';
import { Routes, Route, useLocation, Navigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import AdminSidebar from '../../components/admin/AdminSidebar';
import AdminDashboardHome from '../../components/admin/AdminDashboardHome';
import AdminRooms from '../../components/admin/AdminRooms';
import AdminRoomChangeRequests from '../../components/admin/AdminRoomChangeRequests';
import AdminPayments from '../../components/admin/AdminPayments';
import AdminCustomers from '../../components/admin/AdminCustomers';
import AdminMaintenance from '../../components/admin/AdminMaintenance';
import AdminProfile from '../../components/admin/AdminProfile';
import { Menu } from 'lucide-react';

const AdminDashboard: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user } = useAuth();
  const location = useLocation();

  const getPageTitle = () => {
    const path = location.pathname;
    if (path === '/admin/dashboard' || path === '/admin') return 'Dashboard';
    if (path === '/admin/rooms') return 'Rooms Management';
    if (path === '/admin/room-change-requests') return 'Room Change Requests';
    if (path === '/admin/payments') return 'Payments Management';
    if (path === '/admin/customers') return 'Customers Management';
    if (path === '/admin/maintenance') return 'Maintenance Requests';
    if (path === '/admin/profile') return 'Admin Profile';
    return 'Admin Panel';
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <AdminSidebar onClose={() => setSidebarOpen(false)} />
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top bar */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between px-4 py-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              >
                <Menu className="h-6 w-6" />
              </button>
              <h1 className="text-2xl font-semibold text-gray-900">{getPageTitle()}</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">{user?.name}</p>
                <p className="text-xs text-gray-500">Administrator</p>
              </div>
              <div className="h-8 w-8 bg-gray-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {user?.name?.charAt(0) || 'A'}
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* Main content area */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6">
          <Routes>
            <Route path="/" element={<Navigate to="/admin/dashboard" replace />} />
            <Route path="/dashboard" element={<AdminDashboardHome />} />
            <Route path="/rooms" element={<AdminRooms />} />
            <Route path="/room-change-requests" element={<AdminRoomChangeRequests />} />
            <Route path="/payments" element={<AdminPayments />} />
            <Route path="/customers" element={<AdminCustomers />} />
            <Route path="/maintenance" element={<AdminMaintenance />} />
            <Route path="/profile" element={<AdminProfile />} />
          </Routes>
        </main>
      </div>
    </div>
  );
};

export default AdminDashboard;