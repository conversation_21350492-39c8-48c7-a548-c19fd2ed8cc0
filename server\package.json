{"name": "pg-management-backend", "version": "1.0.0", "description": "Backend server for PG Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seed.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["pg", "management", "express", "mongodb"], "author": "", "license": "ISC"}