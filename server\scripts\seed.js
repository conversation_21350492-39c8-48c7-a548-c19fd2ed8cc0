const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import models
const User = require('../models/User');
const Room = require('../models/Room');
const Payment = require('../models/Payment');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const seedDatabase = async () => {
  try {
    console.log('Starting database seeding...');

    // Clear existing data
    await User.deleteMany({});
    await Room.deleteMany({});
    await Payment.deleteMany({});
    console.log('Cleared existing data');

    // Create admin user
    const adminPassword = await bcrypt.hash('admin123', 12);
    const admin = new User({
      name: 'Admin',
      email: '<EMAIL>',
      password: adminPassword,
      phone: '+91-9876543210',
      role: 'admin',
      status: 'Active'
    });
    await admin.save();
    console.log('Created admin user');

    // Create rooms
    const rooms = [
      { roomNumber: 'A-101', type: 'Single Sharing', floor: '1st Floor', capacity: 1, price: 12000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'A-102', type: 'Single Sharing', floor: '1st Floor', capacity: 1, price: 12000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'A-103', type: 'Single Sharing', floor: '1st Floor', capacity: 1, price: 12000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'A-104', type: 'Single Sharing', floor: '1st Floor', capacity: 1, price: 12000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'A-105', type: 'Single Sharing', floor: '1st Floor', capacity: 1, price: 12000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      
      { roomNumber: 'B-201', type: 'Double Sharing', floor: '2nd Floor', capacity: 2, price: 10000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'B-202', type: 'Double Sharing', floor: '2nd Floor', capacity: 2, price: 10000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'B-203', type: 'Double Sharing', floor: '2nd Floor', capacity: 2, price: 10000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'B-204', type: 'Double Sharing', floor: '2nd Floor', capacity: 2, price: 10000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'B-205', type: 'Double Sharing', floor: '2nd Floor', capacity: 2, price: 10000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      
      { roomNumber: 'C-301', type: 'Triple Sharing', floor: '3rd Floor', capacity: 3, price: 8000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'C-302', type: 'Triple Sharing', floor: '3rd Floor', capacity: 3, price: 8000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'C-303', type: 'Triple Sharing', floor: '3rd Floor', capacity: 3, price: 8000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'C-304', type: 'Triple Sharing', floor: '3rd Floor', capacity: 3, price: 8000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'C-305', type: 'Triple Sharing', floor: '3rd Floor', capacity: 3, price: 8000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      
      { roomNumber: 'D-101', type: 'Four Sharing', floor: '1st Floor', capacity: 4, price: 6000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'D-102', type: 'Four Sharing', floor: '1st Floor', capacity: 4, price: 6000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'D-103', type: 'Four Sharing', floor: '1st Floor', capacity: 4, price: 6000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'D-104', type: 'Four Sharing', floor: '1st Floor', capacity: 4, price: 6000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] },
      { roomNumber: 'D-105', type: 'Four Sharing', floor: '1st Floor', capacity: 4, price: 6000, facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table'] }
    ];

    const createdRooms = await Room.insertMany(rooms);
    console.log(`Created ${createdRooms.length} rooms`);

    // Create sample customers
    const customerPassword = await bcrypt.hash('customer123', 12);
    const customers = [
      {
        name: 'John Doe',
        email: '<EMAIL>',
        password: customerPassword,
        phone: '+91-9876543210',
        location: 'Mumbai, Maharashtra',
        role: 'customer',
        status: 'Active',
        roomNumber: createdRooms[0]._id, // A-101
        rent: 12000
      },
      {
        name: 'Jane Smith',
        email: '<EMAIL>',
        password: customerPassword,
        phone: '+91-9876543211',
        location: 'Delhi, Delhi',
        role: 'customer',
        status: 'Active',
        roomNumber: createdRooms[5]._id, // B-201
        rent: 10000
      },
      {
        name: 'Mike Johnson',
        email: '<EMAIL>',
        password: customerPassword,
        phone: '+91-9876543212',
        location: 'Bangalore, Karnataka',
        role: 'customer',
        status: 'Pending',
        roomNumber: createdRooms[10]._id, // C-301
        rent: 8000
      }
    ];

    const createdCustomers = await User.insertMany(customers);
    console.log(`Created ${createdCustomers.length} customers`);

    // Update room occupants
    await Room.findByIdAndUpdate(createdRooms[0]._id, { 
      $push: { occupants: createdCustomers[0]._id },
      status: 'Occupied'
    });
    await Room.findByIdAndUpdate(createdRooms[5]._id, { 
      $push: { occupants: createdCustomers[1]._id },
      status: 'Partially Occupied'
    });
    await Room.findByIdAndUpdate(createdRooms[10]._id, { 
      $push: { occupants: createdCustomers[2]._id },
      status: 'Partially Occupied'
    });

    console.log('Database seeding completed successfully!');
    console.log('\nLogin credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Customer: <EMAIL> / customer123');
    console.log('Customer: <EMAIL> / customer123');
    console.log('Customer: <EMAIL> / customer123');

  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    mongoose.connection.close();
  }
};

seedDatabase();
