const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const Room = require('../models/Room');
const Payment = require('../models/Payment');
const MaintenanceRequest = require('../models/MaintenanceRequest');
const RoomChangeRequest = require('../models/RoomChangeRequest');
const { adminAuth } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/admin/customers
// @desc    Get all customers
// @access  Admin only
router.get('/customers', adminAuth, async (req, res) => {
  try {
    const { status, search, page = 1, limit = 10 } = req.query;
    
    let query = { role: 'customer' };
    
    // Filter by status
    if (status && status !== 'all') {
      query.status = status.charAt(0).toUpperCase() + status.slice(1);
    }
    
    // Search functionality
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } },
        { roomNumber: { $regex: search, $options: 'i' } }
      ];
    }
    
    const customers = await User.find(query)
      .select('-password')
      .populate('roomNumber', 'roomNumber type price')
      .sort({ registeredOn: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await User.countDocuments(query);
    
    res.json({
      customers,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    console.error('Get customers error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/admin/customers/:id
// @desc    Get customer by ID
// @access  Admin only
router.get('/customers/:id', adminAuth, async (req, res) => {
  try {
    const customer = await User.findById(req.params.id)
      .select('-password')
      .populate('roomNumber', 'roomNumber type price facilities');
    
    if (!customer || customer.role !== 'customer') {
      return res.status(404).json({ message: 'Customer not found' });
    }
    
    res.json(customer);
  } catch (error) {
    console.error('Get customer error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/admin/customers/:id
// @desc    Update customer
// @access  Admin only
router.put('/customers/:id', [
  adminAuth,
  body('name').optional().trim().isLength({ min: 2 }),
  body('email').optional().isEmail(),
  body('phone').optional().isMobilePhone(),
  body('status').optional().isIn(['Active', 'Pending', 'Inactive']),
  body('roomNumber').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    const customer = await User.findById(req.params.id);
    if (!customer || customer.role !== 'customer') {
      return res.status(404).json({ message: 'Customer not found' });
    }
    
    const updateData = req.body;
    delete updateData.password; // Prevent password updates through this route
    delete updateData.role; // Prevent role changes
    
    const updatedCustomer = await User.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');
    
    res.json({
      message: 'Customer updated successfully',
      customer: updatedCustomer
    });
  } catch (error) {
    console.error('Update customer error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   DELETE /api/admin/customers/:id
// @desc    Delete customer
// @access  Admin only
router.delete('/customers/:id', adminAuth, async (req, res) => {
  try {
    const customer = await User.findById(req.params.id);
    if (!customer || customer.role !== 'customer') {
      return res.status(404).json({ message: 'Customer not found' });
    }
    
    // Check if customer has active bookings or payments
    const activePayments = await Payment.countDocuments({ 
      customer: req.params.id, 
      status: { $in: ['Pending', 'Overdue'] } 
    });
    
    if (activePayments > 0) {
      return res.status(400).json({ 
        message: 'Cannot delete customer with active payments' 
      });
    }
    
    await User.findByIdAndDelete(req.params.id);
    
    res.json({ message: 'Customer deleted successfully' });
  } catch (error) {
    console.error('Delete customer error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/admin/dashboard/stats
// @desc    Get dashboard statistics
// @access  Admin only
router.get('/dashboard/stats', adminAuth, async (req, res) => {
  try {
    const totalCustomers = await User.countDocuments({ role: 'customer' });
    const activeCustomers = await User.countDocuments({ role: 'customer', status: 'Active' });
    const pendingCustomers = await User.countDocuments({ role: 'customer', status: 'Pending' });
    
    const totalRooms = await Room.countDocuments();
    const availableRooms = await Room.countDocuments({ status: 'Available' });
    const occupiedRooms = await Room.countDocuments({ status: 'Occupied' });
    
    const totalPayments = await Payment.countDocuments();
    const pendingPayments = await Payment.countDocuments({ status: 'Pending' });
    const overduePayments = await Payment.countDocuments({ status: 'Overdue' });
    
    // Calculate monthly revenue
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);
    
    const monthlyRevenue = await Payment.aggregate([
      {
        $match: {
          paymentDate: { $gte: currentMonth },
          status: 'Completed'
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$finalAmount' }
        }
      }
    ]);
    
    res.json({
      customers: {
        total: totalCustomers,
        active: activeCustomers,
        pending: pendingCustomers
      },
      rooms: {
        total: totalRooms,
        available: availableRooms,
        occupied: occupiedRooms
      },
      payments: {
        total: totalPayments,
        pending: pendingPayments,
        overdue: overduePayments
      },
      monthlyRevenue: monthlyRevenue[0]?.total || 0
    });
  } catch (error) {
    console.error('Get dashboard stats error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/admin/maintenance-requests
// @desc    Get all maintenance requests
// @access  Admin only
router.get('/maintenance-requests', adminAuth, async (req, res) => {
  try {
    const requests = await MaintenanceRequest.find()
      .populate('customer', 'name email phone')
      .populate('room', 'roomNumber type')
      .sort({ requestDate: -1 });

    res.json(requests);
  } catch (error) {
    console.error('Get maintenance requests error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/admin/maintenance-requests/:id
// @desc    Update maintenance request
// @access  Admin only
router.put('/maintenance-requests/:id', adminAuth, async (req, res) => {
  try {
    const { status, assignedTo, estimatedCost, actualCost, notes } = req.body;

    const request = await MaintenanceRequest.findById(req.params.id);
    if (!request) {
      return res.status(404).json({ message: 'Maintenance request not found' });
    }

    const updateData = {};
    if (status) updateData.status = status;
    if (assignedTo) updateData.assignedTo = assignedTo;
    if (estimatedCost !== undefined) updateData.estimatedCost = estimatedCost;
    if (actualCost !== undefined) updateData.actualCost = actualCost;
    if (notes) updateData.notes = notes;

    if (status === 'Completed') {
      updateData.completedDate = new Date();
    }

    const updatedRequest = await MaintenanceRequest.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).populate(['customer', 'room'], 'name email phone roomNumber type');

    res.json({
      message: 'Maintenance request updated successfully',
      request: updatedRequest
    });
  } catch (error) {
    console.error('Update maintenance request error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   DELETE /api/admin/maintenance-requests/:id
// @desc    Delete maintenance request
// @access  Admin only
router.delete('/maintenance-requests/:id', adminAuth, async (req, res) => {
  try {
    const request = await MaintenanceRequest.findById(req.params.id);
    if (!request) {
      return res.status(404).json({ message: 'Maintenance request not found' });
    }

    await MaintenanceRequest.findByIdAndDelete(req.params.id);

    res.json({ message: 'Maintenance request deleted successfully' });
  } catch (error) {
    console.error('Delete maintenance request error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/admin/room-change-requests
// @desc    Get all room change requests
// @access  Admin only
router.get('/room-change-requests', adminAuth, async (req, res) => {
  try {
    const requests = await RoomChangeRequest.find()
      .populate('customer', 'name email phone')
      .populate('currentRoom', 'roomNumber type price')
      .populate('requestedRoom', 'roomNumber type price')
      .sort({ requestDate: -1 });

    res.json(requests);
  } catch (error) {
    console.error('Get room change requests error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/admin/room-change-requests/:id/approve
// @desc    Approve room change request
// @access  Admin only
router.put('/room-change-requests/:id/approve', adminAuth, async (req, res) => {
  try {
    const { adminNotes } = req.body;

    const request = await RoomChangeRequest.findById(req.params.id);
    if (!request) {
      return res.status(404).json({ message: 'Room change request not found' });
    }

    if (request.status !== 'Pending') {
      return res.status(400).json({ message: 'Request has already been processed' });
    }

    await request.approve(req.user._id, adminNotes);

    res.json({ message: 'Room change request approved successfully' });
  } catch (error) {
    console.error('Approve room change request error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/admin/room-change-requests/:id/reject
// @desc    Reject room change request
// @access  Admin only
router.put('/room-change-requests/:id/reject', adminAuth, async (req, res) => {
  try {
    const { adminNotes } = req.body;

    const request = await RoomChangeRequest.findById(req.params.id);
    if (!request) {
      return res.status(404).json({ message: 'Room change request not found' });
    }

    if (request.status !== 'Pending') {
      return res.status(400).json({ message: 'Request has already been processed' });
    }

    await request.reject(req.user._id, adminNotes);

    res.json({ message: 'Room change request rejected successfully' });
  } catch (error) {
    console.error('Reject room change request error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
