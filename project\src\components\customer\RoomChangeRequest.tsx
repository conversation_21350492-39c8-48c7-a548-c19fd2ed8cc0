import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { 
  ArrowLeftRight, 
  Home, 
  Users, 
  Send,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

const RoomChangeRequest: React.FC = () => {
  const { user } = useAuth();
  const [selectedRoom, setSelectedRoom] = useState('');
  const [reason, setReason] = useState('');

  const currentRoom = {
    roomNumber: user?.roomNumber || 'A-101',
    type: 'Single Sharing',
    rent: 12000,
    floor: '1st Floor'
  };

  const availableRooms = [
    { id: 'B-201', type: 'Double Sharing', rent: 10000, floor: '2nd Floor', available: true },
    { id: 'C-301', type: 'Triple Sharing', rent: 8000, floor: '3rd Floor', available: true },
    { id: 'D-102', type: 'Four Sharing', rent: 6000, floor: '1st Floor', available: true },
    { id: 'A-105', type: 'Single Sharing', rent: 12000, floor: '1st Floor', available: false },
  ];

  const requestHistory = [
    {
      id: 1,
      requestDate: '2024-01-20',
      fromRoom: 'A-101',
      toRoom: 'B-201',
      status: 'Pending',
      statusColor: 'text-yellow-600 bg-yellow-50',
      reason: 'Need double sharing for cost optimization'
    },
    {
      id: 2,
      requestDate: '2023-12-15',
      fromRoom: 'A-102',
      toRoom: 'A-101',
      status: 'Approved',
      statusColor: 'text-green-600 bg-green-50',
      reason: 'Preference for single sharing'
    }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedRoom || !reason.trim()) {
      alert('Please select a room and provide a reason');
      return;
    }
    alert('Room change request submitted successfully!');
    setSelectedRoom('');
    setReason('');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center space-x-3">
          <div className="bg-purple-100 p-3 rounded-full">
            <ArrowLeftRight className="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Room Change Request</h2>
            <p className="text-gray-600">Request to change your current room</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current Room Details */}
        <div className="bg-white rounded-xl shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Current Room Details</h3>
          </div>
          <div className="p-6">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center space-x-3 mb-4">
                <div className="bg-blue-100 p-2 rounded-full">
                  <Home className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">{currentRoom.roomNumber}</h4>
                  <p className="text-sm text-gray-600">{currentRoom.type}</p>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Room Type:</span>
                  <span className="font-medium">{currentRoom.type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Floor:</span>
                  <span className="font-medium">{currentRoom.floor}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Monthly Rent:</span>
                  <span className="font-medium text-blue-600">₹{currentRoom.rent.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Room Change Request Form */}
        <div className="bg-white rounded-xl shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Request Room Change</h3>
          </div>
          <div className="p-6">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select New Room
                </label>
                <select
                  value={selectedRoom}
                  onChange={(e) => setSelectedRoom(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  required
                >
                  <option value="">Choose a room...</option>
                  {availableRooms.map((room) => (
                    <option 
                      key={room.id} 
                      value={room.id}
                      disabled={!room.available}
                    >
                      {room.id} - {room.type} - ₹{room.rent.toLocaleString()} {!room.available ? '(Not Available)' : ''}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reason for Change
                </label>
                <textarea
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="Please explain why you want to change your room..."
                  required
                />
              </div>

              {selectedRoom && (
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h4 className="font-medium text-purple-800 mb-2">Selected Room Details:</h4>
                  {availableRooms.find(room => room.id === selectedRoom) && (
                    <div className="text-sm space-y-1">
                      <p><span className="text-gray-600">Room:</span> {selectedRoom}</p>
                      <p><span className="text-gray-600">Type:</span> {availableRooms.find(room => room.id === selectedRoom)?.type}</p>
                      <p><span className="text-gray-600">Rent:</span> ₹{availableRooms.find(room => room.id === selectedRoom)?.rent.toLocaleString()}</p>
                      <p><span className="text-gray-600">Floor:</span> {availableRooms.find(room => room.id === selectedRoom)?.floor}</p>
                    </div>
                  )}
                </div>
              )}

              <button
                type="submit"
                className="w-full bg-purple-600 text-white py-3 rounded-lg hover:bg-purple-700 focus:ring-4 focus:ring-purple-200 transition-colors font-semibold flex items-center justify-center space-x-2"
              >
                <Send className="h-5 w-5" />
                <span>Submit Request</span>
              </button>
            </form>
          </div>
        </div>
      </div>

      {/* Available Rooms */}
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Available Rooms</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {availableRooms.map((room) => (
              <div 
                key={room.id} 
                className={`p-4 rounded-lg border-2 transition-colors ${
                  room.available 
                    ? 'border-gray-200 hover:border-purple-300 bg-white' 
                    : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <Home className={`h-5 w-5 ${room.available ? 'text-purple-600' : 'text-gray-400'}`} />
                    <span className={`font-semibold ${room.available ? 'text-gray-900' : 'text-gray-500'}`}>
                      {room.id}
                    </span>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    room.available 
                      ? 'text-green-600 bg-green-50' 
                      : 'text-red-600 bg-red-50'
                  }`}>
                    {room.available ? 'Available' : 'Occupied'}
                  </span>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Type:</span>
                    <span className={room.available ? 'text-gray-900' : 'text-gray-500'}>
                      {room.type}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Rent:</span>
                    <span className={`font-medium ${room.available ? 'text-purple-600' : 'text-gray-500'}`}>
                      ₹{room.rent.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Floor:</span>
                    <span className={room.available ? 'text-gray-900' : 'text-gray-500'}>
                      {room.floor}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Request History */}
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Request History</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {requestHistory.map((request) => (
              <div key={request.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <Home className="h-4 w-4 text-gray-400" />
                      <span className="font-medium">{request.fromRoom}</span>
                      <ArrowLeftRight className="h-4 w-4 text-gray-400" />
                      <span className="font-medium">{request.toRoom}</span>
                    </div>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${request.statusColor} flex items-center space-x-1`}>
                    {request.status === 'Pending' ? (
                      <Clock className="h-3 w-3" />
                    ) : request.status === 'Approved' ? (
                      <CheckCircle className="h-3 w-3" />
                    ) : (
                      <AlertCircle className="h-3 w-3" />
                    )}
                    <span>{request.status}</span>
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{request.reason}</p>
                <p className="text-xs text-gray-500">Requested on {request.requestDate}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoomChangeRequest;