import React, { useState } from 'react';
import { Routes, Route, Link, useLocation, Navigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import CustomerSidebar from '../../components/customer/CustomerSidebar';
import CustomerDashboardHome from '../../components/customer/CustomerDashboardHome';
import MyRoom from '../../components/customer/MyRoom';
import Payments from '../../components/customer/Payments';
import RoomChangeRequest from '../../components/customer/RoomChangeRequest';
import MaintenanceRequests from '../../components/customer/MaintenanceRequests';
import Notifications from '../../components/customer/Notifications';
import MyProfile from '../../components/customer/MyProfile';
import { Menu, X } from 'lucide-react';

const CustomerDashboard: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user } = useAuth();
  const location = useLocation();

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <CustomerSidebar onClose={() => setSidebarOpen(false)} />
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top bar */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between px-4 py-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              >
                <Menu className="h-6 w-6" />
              </button>
              <h1 className="text-2xl font-semibold text-gray-900">
                {location.pathname === '/customer/dashboard' || location.pathname === '/customer' ? 'Dashboard' : 
                 location.pathname.split('/').pop()?.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">{user?.name}</p>
                <p className="text-xs text-gray-500">{user?.roomNumber || 'No room assigned'}</p>
              </div>
              <div className="h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {user?.name?.charAt(0) || 'U'}
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* Main content area */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6">
          <Routes>
            <Route path="/" element={<Navigate to="/customer/dashboard" replace />} />
            <Route path="/dashboard" element={<CustomerDashboardHome />} />
            <Route path="/my-room" element={<MyRoom />} />
            <Route path="/payments" element={<Payments />} />
            <Route path="/room-change-request" element={<RoomChangeRequest />} />
            <Route path="/maintenance-requests" element={<MaintenanceRequests />} />
            <Route path="/notifications" element={<Notifications />} />
            <Route path="/my-profile" element={<MyProfile />} />
          </Routes>
        </main>
      </div>
    </div>
  );
};

export default CustomerDashboard;