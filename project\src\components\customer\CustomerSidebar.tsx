import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { 
  Home, 
  Bed, 
  CreditCard, 
  ArrowLeftRight, 
  Wrench, 
  Bell, 
  User, 
  LogOut,
  X
} from 'lucide-react';

interface CustomerSidebarProps {
  onClose: () => void;
}

const CustomerSidebar: React.FC<CustomerSidebarProps> = ({ onClose }) => {
  const location = useLocation();
  const { logout } = useAuth();

  const menuItems = [
    { path: '/customer/dashboard', icon: Home, label: 'Dashboard' },
    { path: '/customer/my-room', icon: Bed, label: 'My Room' },
    { path: '/customer/payments', icon: CreditCard, label: 'Payments' },
    { path: '/customer/room-change-request', icon: ArrowLeftRight, label: 'Room Change Request' },
    { path: '/customer/maintenance-requests', icon: Wrench, label: 'Maintenance Requests' },
    { path: '/customer/notifications', icon: Bell, label: 'Notifications' },
    { path: '/customer/my-profile', icon: User, label: 'My Profile' },
  ];

  const handleLogout = () => {
    logout();
    onClose();
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Home className="h-8 w-8 text-blue-600" />
          <h2 className="text-xl font-bold text-gray-900">SLNS PG</h2>
        </div>
        <button
          onClick={onClose}
          className="lg:hidden p-1 rounded-md text-gray-400 hover:text-gray-600"
        >
          <X className="h-6 w-6" />
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {menuItems.map((item) => {
          const isActive = location.pathname === item.path;
          return (
            <Link
              key={item.path}
              to={item.path}
              onClick={onClose}
              className={`
                flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors
                ${isActive 
                  ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-700' 
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                }
              `}
            >
              <item.icon className={`h-5 w-5 ${isActive ? 'text-blue-700' : 'text-gray-400'}`} />
              <span className="font-medium">{item.label}</span>
            </Link>
          );
        })}
      </nav>

      {/* Logout */}
      <div className="p-4 border-t border-gray-200">
        <button
          onClick={handleLogout}
          className="flex items-center space-x-3 w-full px-4 py-3 text-gray-700 hover:bg-red-50 hover:text-red-700 rounded-lg transition-colors"
        >
          <LogOut className="h-5 w-5" />
          <span className="font-medium">Logout</span>
        </button>
      </div>
    </div>
  );
};

export default CustomerSidebar;