const express = require('express');
const { body, validationResult } = require('express-validator');
const Room = require('../models/Room');
const User = require('../models/User');
const { auth, adminAuth } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/rooms
// @desc    Get all rooms
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const { status, type, available } = req.query;
    
    let query = {};
    
    if (status) {
      query.status = status;
    }
    
    if (type) {
      query.type = type;
    }
    
    if (available === 'true') {
      query.$or = [
        { status: 'Available' },
        { status: 'Partially Occupied' }
      ];
    }
    
    const rooms = await Room.find(query)
      .populate('occupants', 'name email phone')
      .sort({ roomNumber: 1 });
    
    res.json(rooms);
  } catch (error) {
    console.error('Get rooms error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/rooms/:id
// @desc    Get room by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const room = await Room.findById(req.params.id)
      .populate('occupants', 'name email phone status');
    
    if (!room) {
      return res.status(404).json({ message: 'Room not found' });
    }
    
    res.json(room);
  } catch (error) {
    console.error('Get room error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/rooms
// @desc    Create new room
// @access  Admin only
router.post('/', [
  adminAuth,
  body('roomNumber').trim().isLength({ min: 1 }).withMessage('Room number is required'),
  body('type').isIn(['Single Sharing', 'Double Sharing', 'Triple Sharing', 'Four Sharing']),
  body('floor').trim().isLength({ min: 1 }).withMessage('Floor is required'),
  body('capacity').isInt({ min: 1, max: 4 }).withMessage('Capacity must be between 1 and 4'),
  body('price').isFloat({ min: 0 }).withMessage('Price must be a positive number')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    const { roomNumber, type, floor, capacity, price, facilities, description } = req.body;
    
    // Check if room number already exists
    const existingRoom = await Room.findOne({ roomNumber: roomNumber.toUpperCase() });
    if (existingRoom) {
      return res.status(400).json({ message: 'Room number already exists' });
    }
    
    const room = new Room({
      roomNumber: roomNumber.toUpperCase(),
      type,
      floor,
      capacity,
      price,
      facilities: facilities || [],
      description
    });
    
    await room.save();
    
    res.status(201).json({
      message: 'Room created successfully',
      room
    });
  } catch (error) {
    console.error('Create room error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/rooms/:id
// @desc    Update room
// @access  Admin only
router.put('/:id', [
  adminAuth,
  body('roomNumber').optional().trim().isLength({ min: 1 }),
  body('type').optional().isIn(['Single Sharing', 'Double Sharing', 'Triple Sharing', 'Four Sharing']),
  body('floor').optional().trim().isLength({ min: 1 }),
  body('capacity').optional().isInt({ min: 1, max: 4 }),
  body('price').optional().isFloat({ min: 0 }),
  body('status').optional().isIn(['Available', 'Occupied', 'Partially Occupied', 'Maintenance'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    const room = await Room.findById(req.params.id);
    if (!room) {
      return res.status(404).json({ message: 'Room not found' });
    }
    
    const updateData = req.body;
    if (updateData.roomNumber) {
      updateData.roomNumber = updateData.roomNumber.toUpperCase();
      
      // Check if new room number already exists
      const existingRoom = await Room.findOne({ 
        roomNumber: updateData.roomNumber,
        _id: { $ne: req.params.id }
      });
      if (existingRoom) {
        return res.status(400).json({ message: 'Room number already exists' });
      }
    }
    
    const updatedRoom = await Room.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).populate('occupants', 'name email phone');
    
    res.json({
      message: 'Room updated successfully',
      room: updatedRoom
    });
  } catch (error) {
    console.error('Update room error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   DELETE /api/rooms/:id
// @desc    Delete room
// @access  Admin only
router.delete('/:id', adminAuth, async (req, res) => {
  try {
    const room = await Room.findById(req.params.id);
    if (!room) {
      return res.status(404).json({ message: 'Room not found' });
    }
    
    // Check if room has occupants
    if (room.occupants && room.occupants.length > 0) {
      return res.status(400).json({ 
        message: 'Cannot delete room with occupants' 
      });
    }
    
    await Room.findByIdAndDelete(req.params.id);
    
    res.json({ message: 'Room deleted successfully' });
  } catch (error) {
    console.error('Delete room error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/rooms/:id/assign
// @desc    Assign customer to room
// @access  Admin only
router.post('/:id/assign', [
  adminAuth,
  body('customerId').isMongoId().withMessage('Valid customer ID required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    const room = await Room.findById(req.params.id);
    if (!room) {
      return res.status(404).json({ message: 'Room not found' });
    }
    
    const customer = await User.findById(req.body.customerId);
    if (!customer || customer.role !== 'customer') {
      return res.status(404).json({ message: 'Customer not found' });
    }
    
    // Check if room has space
    if (room.currentOccupancy >= room.capacity) {
      return res.status(400).json({ message: 'Room is at full capacity' });
    }
    
    // Check if customer is already assigned to this room
    if (room.occupants.includes(customer._id)) {
      return res.status(400).json({ message: 'Customer already assigned to this room' });
    }
    
    // Remove customer from previous room if any
    if (customer.roomNumber) {
      await Room.findByIdAndUpdate(customer.roomNumber, {
        $pull: { occupants: customer._id }
      });
    }
    
    // Add customer to new room
    room.occupants.push(customer._id);
    await room.updateStatus();
    
    // Update customer's room number
    customer.roomNumber = room._id;
    customer.rent = room.price;
    customer.status = 'Active';
    await customer.save();
    
    res.json({
      message: 'Customer assigned to room successfully',
      room: await Room.findById(room._id).populate('occupants', 'name email phone')
    });
  } catch (error) {
    console.error('Assign room error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
