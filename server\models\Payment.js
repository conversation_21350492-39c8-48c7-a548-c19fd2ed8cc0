const mongoose = require('mongoose');

const paymentSchema = new mongoose.Schema({
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Customer is required']
  },
  room: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Room',
    required: [true, 'Room is required']
  },
  amount: {
    type: Number,
    required: [true, 'Payment amount is required'],
    min: [0, 'Amount cannot be negative']
  },
  paymentDate: {
    type: Date,
    default: Date.now
  },
  dueDate: {
    type: Date,
    required: [true, 'Due date is required']
  },
  method: {
    type: String,
    enum: ['UPI', 'Bank Transfer', 'Card', 'Cash', 'Pending'],
    default: 'Pending'
  },
  transactionId: {
    type: String,
    trim: true,
    sparse: true // Allows multiple null values
  },
  status: {
    type: String,
    enum: ['Pending', 'Completed', 'Failed', 'Overdue'],
    default: 'Pending'
  },
  invoice: {
    type: String,
    unique: true,
    trim: true
  },
  description: {
    type: String,
    trim: true,
    default: 'Monthly rent payment'
  },
  lateFee: {
    type: Number,
    default: 0,
    min: [0, 'Late fee cannot be negative']
  },
  discount: {
    type: Number,
    default: 0,
    min: [0, 'Discount cannot be negative']
  },
  finalAmount: {
    type: Number,
    min: [0, 'Final amount cannot be negative']
  }
}, {
  timestamps: true
});

// Generate invoice number before saving
paymentSchema.pre('save', function(next) {
  if (!this.invoice) {
    const timestamp = Date.now().toString().slice(-6);
    this.invoice = `INV-${timestamp}`;
  }
  
  // Calculate final amount
  this.finalAmount = this.amount + this.lateFee - this.discount;
  
  next();
});

// Check if payment is overdue
paymentSchema.methods.checkOverdue = function() {
  if (this.status === 'Pending' && new Date() > this.dueDate) {
    this.status = 'Overdue';
    return this.save();
  }
  return Promise.resolve(this);
};

// Static method to get payment statistics
paymentSchema.statics.getStats = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalAmount: { $sum: '$finalAmount' }
      }
    }
  ]);
  
  return stats;
};

module.exports = mongoose.model('Payment', paymentSchema);
