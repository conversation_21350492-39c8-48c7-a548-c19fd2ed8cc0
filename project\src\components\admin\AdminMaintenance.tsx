import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  Filter, 
  <PERSON>, 
  CheckCircle, 
  Clock,
  AlertTriangle,
  User,
  Calendar
} from 'lucide-react';

const AdminMaintenance: React.FC = () => {
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');

  const maintenanceRequests = [
    {
      id: 1,
      customer: '<PERSON>',
      room: 'A-101',
      requestType: 'Plumbing',
      priority: 'high',
      priorityColor: 'text-orange-600 bg-orange-50',
      description: 'Water leakage in bathroom tap',
      status: 'In Progress',
      statusColor: 'text-blue-600 bg-blue-50',
      requestDate: '2024-01-25',
      assignedTo: 'Maintenance Team A',
      estimatedCompletion: '2024-01-27'
    },
    {
      id: 2,
      customer: '<PERSON>',
      room: 'B-201',
      requestType: 'Electrical',
      priority: 'medium',
      priorityColor: 'text-yellow-600 bg-yellow-50',
      description: 'Light bulb replacement needed in bedroom',
      status: 'Completed',
      statusColor: 'text-green-600 bg-green-50',
      requestDate: '2024-01-20',
      assignedTo: 'Maintenance Team B',
      completedDate: '2024-01-22'
    },
    {
      id: 3,
      customer: '<PERSON> <PERSON>',
      room: 'C-301',
      requestType: 'AC/Heating',
      priority: 'urgent',
      priorityColor: 'text-red-600 bg-red-50',
      description: 'AC not working properly, room too hot',
      status: 'Pending',
      statusColor: 'text-yellow-600 bg-yellow-50',
      requestDate: '2024-01-28',
      assignedTo: null,
      estimatedCompletion: null
    },
    {
      id: 4,
      customer: 'Alice Brown',
      room: 'D-101',
      requestType: 'Internet/WiFi',
      priority: 'low',
      priorityColor: 'text-green-600 bg-green-50',
      description: 'Slow internet connection in room',
      status: 'Rejected',
      statusColor: 'text-red-600 bg-red-50',
      requestDate: '2024-01-15',
      assignedTo: null,
      rejectionReason: 'Issue with ISP, not building maintenance'
    },
    {
      id: 5,
      customer: 'Bob Wilson',
      room: 'A-102',
      requestType: 'Furniture',
      priority: 'medium',
      priorityColor: 'text-yellow-600 bg-yellow-50',
      description: 'Study table drawer is broken',
      status: 'In Progress',
      statusColor: 'text-blue-600 bg-blue-50',
      requestDate: '2024-01-26',
      assignedTo: 'Maintenance Team C',
      estimatedCompletion: '2024-01-29'
    }
  ];

  const filteredRequests = maintenanceRequests.filter(request => {
    const matchesStatus = filterStatus === 'all' || request.status.toLowerCase().replace(' ', '') === filterStatus.toLowerCase();
    const matchesPriority = filterPriority === 'all' || request.priority === filterPriority;
    
    return matchesStatus && matchesPriority;
  });

  const pendingCount = maintenanceRequests.filter(r => r.status === 'Pending').length;
  const inProgressCount = maintenanceRequests.filter(r => r.status === 'In Progress').length;
  const completedCount = maintenanceRequests.filter(r => r.status === 'Completed').length;
  const rejectedCount = maintenanceRequests.filter(r => r.status === 'Rejected').length;

  const getPriorityLabel = (priority: string) => {
    const labels = {
      low: 'Low',
      medium: 'Medium',
      high: 'High',
      urgent: 'Urgent'
    };
    return labels[priority as keyof typeof labels] || priority;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center space-x-3">
          <div className="bg-orange-100 p-3 rounded-full">
            <Wrench className="h-6 w-6 text-orange-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Maintenance Requests</h2>
            <p className="text-gray-600">Manage and track all maintenance requests</p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Requests</p>
              <p className="text-2xl font-bold text-gray-900">{maintenanceRequests.length}</p>
            </div>
            <div className="bg-gray-100 p-3 rounded-full">
              <Wrench className="h-6 w-6 text-gray-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">{pendingCount}</p>
            </div>
            <div className="bg-yellow-100 p-3 rounded-full">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">In Progress</p>
              <p className="text-2xl font-bold text-blue-600">{inProgressCount}</p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <AlertTriangle className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">{completedCount}</p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="inprogress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
          <select
            value={filterPriority}
            onChange={(e) => setFilterPriority(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="all">All Priority</option>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="urgent">Urgent</option>
          </select>
        </div>
      </div>

      {/* Requests Table */}
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Maintenance Requests ({filteredRequests.length})
          </h3>
        </div>
        <div className="p-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-sm text-gray-600 border-b border-gray-200">
                  <th className="pb-3">ID</th>
                  <th className="pb-3">Customer</th>
                  <th className="pb-3">Room</th>
                  <th className="pb-3">Type</th>
                  <th className="pb-3">Priority</th>
                  <th className="pb-3">Description</th>
                  <th className="pb-3">Status</th>
                  <th className="pb-3">Request Date</th>
                  <th className="pb-3">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredRequests.map((request) => (
                  <tr key={request.id} className="border-b border-gray-100">
                    <td className="py-4 font-medium text-gray-900">#{request.id}</td>
                    <td className="py-4">
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-gray-900">{request.customer}</span>
                      </div>
                    </td>
                    <td className="py-4">
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium">
                        {request.room}
                      </span>
                    </td>
                    <td className="py-4">
                      <div className="flex items-center space-x-2">
                        <Wrench className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-900">{request.requestType}</span>
                      </div>
                    </td>
                    <td className="py-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${request.priorityColor}`}>
                        {getPriorityLabel(request.priority)}
                      </span>
                    </td>
                    <td className="py-4">
                      <p className="text-sm text-gray-600 max-w-xs truncate">
                        {request.description}
                      </p>
                    </td>
                    <td className="py-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${request.statusColor} flex items-center space-x-1 w-fit`}>
                        {request.status === 'Completed' ? (
                          <CheckCircle className="h-3 w-3" />
                        ) : request.status === 'In Progress' ? (
                          <AlertTriangle className="h-3 w-3" />
                        ) : (
                          <Clock className="h-3 w-3" />
                        )}
                        <span>{request.status}</span>
                      </span>
                    </td>
                    <td className="py-4">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{request.requestDate}</span>
                      </div>
                    </td>
                    <td className="py-4">
                      <button className="text-orange-600 hover:text-orange-800 p-1 rounded hover:bg-orange-100 transition-colors">
                        <Eye className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Request Cards for Mobile View */}
      <div className="lg:hidden space-y-4">
        {filteredRequests.map((request) => (
          <div key={request.id} className="bg-white rounded-xl shadow-sm p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <span className="font-semibold text-gray-900">#{request.id}</span>
                <span className="text-gray-600">-</span>
                <span className="font-medium text-gray-900">{request.customer}</span>
              </div>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${request.statusColor}`}>
                {request.status}
              </span>
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Room:</span>
                <span className="font-medium">{request.room}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Type:</span>
                <span className="font-medium">{request.requestType}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Priority:</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${request.priorityColor}`}>
                  {getPriorityLabel(request.priority)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Date:</span>
                <span className="font-medium">{request.requestDate}</span>
              </div>
            </div>
            
            <p className="text-sm text-gray-600 mt-3 p-2 bg-gray-50 rounded">
              {request.description}
            </p>
            
            <button className="w-full mt-3 bg-orange-50 text-orange-600 py-2 rounded-lg hover:bg-orange-100 transition-colors text-sm font-medium">
              View Details
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AdminMaintenance;