import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { 
  Home, 
  Calendar, 
  CheckCircle, 
  Eye, 
  Bed, 
  Users, 
  Wifi, 
  Car
} from 'lucide-react';

const MyRoom: React.FC = () => {
  const { user } = useAuth();

  const roomDetails = [
    {
      id: 1,
      roomNumber: user?.roomNumber || 'A-101',
      checkInDate: '2024-01-15',
      status: 'Active',
      statusColor: 'text-green-600 bg-green-50',
      roomType: 'Single Sharing',
      floor: '1st Floor',
      rent: 12000,
      facilities: ['WiFi', 'AC', 'Attached Bathroom', 'Study Table']
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center space-x-3">
          <div className="bg-blue-100 p-3 rounded-full">
            <Bed className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">My Room</h2>
            <p className="text-gray-600">View your current room details and status</p>
          </div>
        </div>
      </div>

      {/* Room Details Table */}
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Room Details</h3>
        </div>
        <div className="p-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-sm text-gray-600 border-b border-gray-200">
                  <th className="pb-3">Room Number</th>
                  <th className="pb-3">Check-in Date</th>
                  <th className="pb-3">Status</th>
                  <th className="pb-3">Action</th>
                </tr>
              </thead>
              <tbody>
                {roomDetails.map((room) => (
                  <tr key={room.id} className="border-b border-gray-100">
                    <td className="py-4">
                      <div className="flex items-center space-x-3">
                        <div className="bg-blue-100 p-2 rounded-lg">
                          <Home className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-semibold text-gray-900">{room.roomNumber}</p>
                          <p className="text-sm text-gray-500">{room.roomType}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-900">{room.checkInDate}</span>
                      </div>
                    </td>
                    <td className="py-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${room.statusColor} flex items-center space-x-1 w-fit`}>
                        <CheckCircle className="h-3 w-3" />
                        <span>{room.status}</span>
                      </span>
                    </td>
                    <td className="py-4">
                      <button className="text-blue-600 hover:text-blue-800 font-medium flex items-center space-x-1 bg-blue-50 px-3 py-1 rounded-lg hover:bg-blue-100 transition-colors">
                        <Eye className="h-4 w-4" />
                        <span>View Details</span>
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Detailed Room Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Room Specifications */}
        <div className="bg-white rounded-xl shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Room Specifications</h3>
          </div>
          <div className="p-6 space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Room Number:</span>
              <span className="font-semibold text-gray-900">{roomDetails[0].roomNumber}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Room Type:</span>
              <span className="font-semibold text-gray-900">{roomDetails[0].roomType}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Floor:</span>
              <span className="font-semibold text-gray-900">{roomDetails[0].floor}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Monthly Rent:</span>
              <span className="font-semibold text-green-600">₹{roomDetails[0].rent.toLocaleString()}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Occupancy:</span>
              <div className="flex items-center space-x-1">
                <Users className="h-4 w-4 text-gray-400" />
                <span className="font-semibold text-gray-900">1 person</span>
              </div>
            </div>
          </div>
        </div>

        {/* Room Facilities */}
        <div className="bg-white rounded-xl shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Room Facilities</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-2 gap-4">
              {roomDetails[0].facilities.map((facility, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                  <div className="bg-blue-100 p-2 rounded-full">
                    {facility.includes('WiFi') ? (
                      <Wifi className="h-4 w-4 text-blue-600" />
                    ) : facility.includes('AC') ? (
                      <div className="h-4 w-4 bg-blue-600 rounded"></div>
                    ) : facility.includes('Bathroom') ? (
                      <div className="h-4 w-4 bg-blue-600 rounded"></div>
                    ) : (
                      <div className="h-4 w-4 bg-blue-600 rounded"></div>
                    )}
                  </div>
                  <span className="text-sm font-medium text-gray-700">{facility}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Room History */}
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Room History</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-4 p-4 bg-green-50 rounded-lg">
              <div className="bg-green-100 p-2 rounded-full">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">Room A-101 Check-in</p>
                <p className="text-sm text-gray-600">Checked into single sharing room on January 15, 2024</p>
              </div>
              <span className="text-sm text-gray-500">Jan 15, 2024</span>
            </div>
            
            <div className="flex items-center space-x-4 p-4 bg-blue-50 rounded-lg">
              <div className="bg-blue-100 p-2 rounded-full">
                <Home className="h-5 w-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">Room Assignment</p>
                <p className="text-sm text-gray-600">Room A-101 assigned after registration completion</p>
              </div>
              <span className="text-sm text-gray-500">Jan 14, 2024</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MyRoom;