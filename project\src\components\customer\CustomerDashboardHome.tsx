import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { 
  Home, 
  CreditCard, 
  Bell, 
  CheckCircle, 
  AlertCircle, 
  Calendar,
  Eye
} from 'lucide-react';

const CustomerDashboardHome: React.FC = () => {
  const { user } = useAuth();

  const recentBookings = [
    {
      id: 1,
      room: 'A-101',
      checkInDate: '2024-01-15',
      status: 'Active',
      statusColor: 'text-green-600 bg-green-50'
    },
    {
      id: 2,
      room: 'A-102',
      checkInDate: '2023-12-01',
      status: 'Expired',
      statusColor: 'text-red-600 bg-red-50'
    }
  ];

  const recentPayments = [
    {
      id: 1,
      date: '2024-01-01',
      amount: 12000,
      method: 'UPI',
      status: 'Completed',
      statusColor: 'text-green-600 bg-green-50'
    },
    {
      id: 2,
      date: '2023-12-01',
      amount: 12000,
      method: 'Bank Transfer',
      status: 'Completed',
      statusColor: 'text-green-600 bg-green-50'
    },
    {
      id: 3,
      date: '2024-02-01',
      amount: 12000,
      method: 'Pending',
      status: 'Pending',
      statusColor: 'text-yellow-600 bg-yellow-50'
    }
  ];

  const notifications = [
    {
      id: 1,
      message: 'Monthly rent due on February 1st, 2024',
      type: 'warning',
      time: '2 hours ago'
    },
    {
      id: 2,
      message: 'Maintenance request completed',
      type: 'success',
      time: '1 day ago'
    },
    {
      id: 3,
      message: 'New facility: Gym access now available',
      type: 'info',
      time: '3 days ago'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl text-white p-6">
        <h2 className="text-2xl font-bold mb-2">Welcome back, {user?.name}!</h2>
        <p className="text-blue-100">Here's what's happening with your accommodation</p>
        <div className="mt-4 flex items-center space-x-6">
          <div className="flex items-center space-x-2">
            <Home className="h-5 w-5" />
            <span>Room: {user?.roomNumber || 'Not assigned'}</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5" />
            <span>Status: Active</span>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Room Number</p>
              <p className="text-2xl font-bold text-gray-900">{user?.roomNumber || 'N/A'}</p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <Home className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Status</p>
              <p className="text-lg font-semibold text-green-600">Active</p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Monthly Rent</p>
              <p className="text-2xl font-bold text-gray-900">₹12,000</p>
            </div>
            <div className="bg-yellow-100 p-3 rounded-full">
              <CreditCard className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Notifications</p>
              <p className="text-2xl font-bold text-gray-900">{notifications.length}</p>
            </div>
            <div className="bg-red-100 p-3 rounded-full">
              <Bell className="h-6 w-6 text-red-600" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Bookings */}
        <div className="bg-white rounded-xl shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Bookings</h3>
          </div>
          <div className="p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left text-sm text-gray-600">
                    <th className="pb-3">Room</th>
                    <th className="pb-3">Check-in Date</th>
                    <th className="pb-3">Status</th>
                    <th className="pb-3">Action</th>
                  </tr>
                </thead>
                <tbody className="space-y-2">
                  {recentBookings.map((booking) => (
                    <tr key={booking.id} className="border-t border-gray-100">
                      <td className="py-3 font-medium">{booking.room}</td>
                      <td className="py-3 text-gray-600">{booking.checkInDate}</td>
                      <td className="py-3">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${booking.statusColor}`}>
                          {booking.status}
                        </span>
                      </td>
                      <td className="py-3">
                        <button className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1">
                          <Eye className="h-4 w-4" />
                          <span>View</span>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Recent Payments */}
        <div className="bg-white rounded-xl shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Payments</h3>
          </div>
          <div className="p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left text-sm text-gray-600">
                    <th className="pb-3">Date</th>
                    <th className="pb-3">Amount</th>
                    <th className="pb-3">Method</th>
                    <th className="pb-3">Status</th>
                    <th className="pb-3">Action</th>
                  </tr>
                </thead>
                <tbody className="space-y-2">
                  {recentPayments.map((payment) => (
                    <tr key={payment.id} className="border-t border-gray-100">
                      <td className="py-3 text-sm">{payment.date}</td>
                      <td className="py-3 font-medium">₹{payment.amount.toLocaleString()}</td>
                      <td className="py-3 text-gray-600 text-sm">{payment.method}</td>
                      <td className="py-3">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${payment.statusColor}`}>
                          {payment.status}
                        </span>
                      </td>
                      <td className="py-3">
                        <button className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1">
                          <Eye className="h-4 w-4" />
                          <span>View</span>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Notifications */}
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Notifications</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {notifications.map((notification) => (
              <div key={notification.id} className="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg">
                <div className={`p-2 rounded-full ${
                  notification.type === 'warning' ? 'bg-yellow-100' :
                  notification.type === 'success' ? 'bg-green-100' : 'bg-blue-100'
                }`}>
                  {notification.type === 'warning' ? (
                    <AlertCircle className={`h-4 w-4 ${
                      notification.type === 'warning' ? 'text-yellow-600' :
                      notification.type === 'success' ? 'text-green-600' : 'text-blue-600'
                    }`} />
                  ) : notification.type === 'success' ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <Bell className="h-4 w-4 text-blue-600" />
                  )}
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{notification.message}</p>
                  <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerDashboardHome;