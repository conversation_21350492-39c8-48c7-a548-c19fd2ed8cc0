const mongoose = require('mongoose');

const roomSchema = new mongoose.Schema({
  roomNumber: {
    type: String,
    required: [true, 'Room number is required'],
    unique: true,
    trim: true,
    uppercase: true
  },
  type: {
    type: String,
    required: [true, 'Room type is required'],
    enum: ['Single Sharing', 'Double Sharing', 'Triple Sharing', 'Four Sharing']
  },
  floor: {
    type: String,
    required: [true, 'Floor is required'],
    trim: true
  },
  capacity: {
    type: Number,
    required: [true, 'Room capacity is required'],
    min: [1, 'Capacity must be at least 1'],
    max: [4, 'Capacity cannot exceed 4']
  },
  price: {
    type: Number,
    required: [true, 'Room price is required'],
    min: [0, 'Price cannot be negative']
  },
  status: {
    type: String,
    enum: ['Available', 'Occupied', 'Partially Occupied', 'Maintenance'],
    default: 'Available'
  },
  occupants: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  facilities: [{
    type: String,
    trim: true
  }],
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  images: [{
    type: String,
    trim: true
  }]
}, {
  timestamps: true
});

// Virtual for current occupancy
roomSchema.virtual('currentOccupancy').get(function() {
  return this.occupants ? this.occupants.length : 0;
});

// Virtual for availability
roomSchema.virtual('isAvailable').get(function() {
  return this.status === 'Available' || 
         (this.status === 'Partially Occupied' && this.currentOccupancy < this.capacity);
});

// Update status based on occupancy
roomSchema.methods.updateStatus = function() {
  const occupancy = this.currentOccupancy;
  
  if (occupancy === 0) {
    this.status = 'Available';
  } else if (occupancy < this.capacity) {
    this.status = 'Partially Occupied';
  } else if (occupancy === this.capacity) {
    this.status = 'Occupied';
  }
  
  return this.save();
};

// Ensure virtuals are included in JSON
roomSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Room', roomSchema);
