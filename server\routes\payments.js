const express = require('express');
const { body, validationResult } = require('express-validator');
const Payment = require('../models/Payment');
const User = require('../models/User');
const Room = require('../models/Room');
const { auth, adminAuth, customerAuth } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/payments
// @desc    Get all payments (admin) or user payments (customer)
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    let query = {};
    
    // If customer, only show their payments
    if (req.user.role === 'customer') {
      query.customer = req.user._id;
    }
    
    const { status, page = 1, limit = 10 } = req.query;
    
    if (status) {
      query.status = status;
    }
    
    const payments = await Payment.find(query)
      .populate('customer', 'name email phone')
      .populate('room', 'roomNumber type')
      .sort({ paymentDate: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await Payment.countDocuments(query);
    
    res.json({
      payments,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    console.error('Get payments error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/payments/:id
// @desc    Get payment by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    let query = { _id: req.params.id };
    
    // If customer, ensure they can only see their own payments
    if (req.user.role === 'customer') {
      query.customer = req.user._id;
    }
    
    const payment = await Payment.findOne(query)
      .populate('customer', 'name email phone')
      .populate('room', 'roomNumber type price');
    
    if (!payment) {
      return res.status(404).json({ message: 'Payment not found' });
    }
    
    res.json(payment);
  } catch (error) {
    console.error('Get payment error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/payments
// @desc    Create new payment
// @access  Admin only
router.post('/', [
  adminAuth,
  body('customer').isMongoId().withMessage('Valid customer ID required'),
  body('room').isMongoId().withMessage('Valid room ID required'),
  body('amount').isFloat({ min: 0 }).withMessage('Amount must be a positive number'),
  body('dueDate').isISO8601().withMessage('Valid due date required'),
  body('method').optional().isIn(['UPI', 'Bank Transfer', 'Card', 'Cash', 'Pending'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    const { customer, room, amount, dueDate, method, description, lateFee, discount } = req.body;
    
    // Verify customer and room exist
    const customerExists = await User.findById(customer);
    const roomExists = await Room.findById(room);
    
    if (!customerExists || customerExists.role !== 'customer') {
      return res.status(404).json({ message: 'Customer not found' });
    }
    
    if (!roomExists) {
      return res.status(404).json({ message: 'Room not found' });
    }
    
    const payment = new Payment({
      customer,
      room,
      amount,
      dueDate,
      method: method || 'Pending',
      description,
      lateFee: lateFee || 0,
      discount: discount || 0
    });
    
    await payment.save();
    await payment.populate(['customer', 'room'], 'name email phone roomNumber type');
    
    res.status(201).json({
      message: 'Payment created successfully',
      payment
    });
  } catch (error) {
    console.error('Create payment error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/payments/:id
// @desc    Update payment
// @access  Admin only
router.put('/:id', [
  adminAuth,
  body('amount').optional().isFloat({ min: 0 }),
  body('status').optional().isIn(['Pending', 'Completed', 'Failed', 'Overdue']),
  body('method').optional().isIn(['UPI', 'Bank Transfer', 'Card', 'Cash', 'Pending']),
  body('transactionId').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    const payment = await Payment.findById(req.params.id);
    if (!payment) {
      return res.status(404).json({ message: 'Payment not found' });
    }
    
    const updateData = req.body;
    
    // If marking as completed, set payment date
    if (updateData.status === 'Completed' && payment.status !== 'Completed') {
      updateData.paymentDate = new Date();
      
      // Update customer's last payment date
      await User.findByIdAndUpdate(payment.customer, {
        lastPayment: new Date()
      });
    }
    
    const updatedPayment = await Payment.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).populate(['customer', 'room'], 'name email phone roomNumber type');
    
    res.json({
      message: 'Payment updated successfully',
      payment: updatedPayment
    });
  } catch (error) {
    console.error('Update payment error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   DELETE /api/payments/:id
// @desc    Delete payment
// @access  Admin only
router.delete('/:id', adminAuth, async (req, res) => {
  try {
    const payment = await Payment.findById(req.params.id);
    if (!payment) {
      return res.status(404).json({ message: 'Payment not found' });
    }
    
    await Payment.findByIdAndDelete(req.params.id);
    
    res.json({ message: 'Payment deleted successfully' });
  } catch (error) {
    console.error('Delete payment error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/payments/generate-monthly
// @desc    Generate monthly payments for all active customers
// @access  Admin only
router.post('/generate-monthly', adminAuth, async (req, res) => {
  try {
    const { month, year } = req.body;
    
    if (!month || !year) {
      return res.status(400).json({ message: 'Month and year are required' });
    }
    
    // Get all active customers with rooms
    const customers = await User.find({
      role: 'customer',
      status: 'Active',
      roomNumber: { $exists: true, $ne: null }
    }).populate('roomNumber', 'price');
    
    const payments = [];
    const dueDate = new Date(year, month - 1, 5); // 5th of the month
    
    for (const customer of customers) {
      // Check if payment already exists for this month
      const existingPayment = await Payment.findOne({
        customer: customer._id,
        paymentDate: {
          $gte: new Date(year, month - 1, 1),
          $lt: new Date(year, month, 1)
        }
      });
      
      if (!existingPayment) {
        const payment = new Payment({
          customer: customer._id,
          room: customer.roomNumber._id,
          amount: customer.roomNumber.price,
          dueDate,
          description: `Monthly rent for ${month}/${year}`
        });
        
        await payment.save();
        payments.push(payment);
      }
    }
    
    res.json({
      message: `Generated ${payments.length} monthly payments`,
      payments: payments.length
    });
  } catch (error) {
    console.error('Generate monthly payments error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
