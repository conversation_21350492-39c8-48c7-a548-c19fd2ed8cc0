import React, { useState } from 'react';
import { 
  Bed, 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit3, 
  Trash2,
  Users,
  CheckCircle,
  XCircle
} from 'lucide-react';

const AdminRooms: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showAddRoom, setShowAddRoom] = useState(false);

  const rooms = [
    {
      id: 1,
      roomNumber: 'A-101',
      type: 'Single Sharing',
      floor: '1st Floor',
      capacity: 1,
      price: 12000,
      status: 'Occupied',
      statusColor: 'text-red-600 bg-red-50',
      occupant: '<PERSON>'
    },
    {
      id: 2,
      roomNumber: 'A-102',
      type: 'Single Sharing',
      floor: '1st Floor',
      capacity: 1,
      price: 12000,
      status: 'Available',
      statusColor: 'text-green-600 bg-green-50',
      occupant: null
    },
    {
      id: 3,
      roomNumber: 'B-201',
      type: 'Double Sharing',
      floor: '2nd Floor',
      capacity: 2,
      price: 10000,
      status: 'Occupied',
      statusColor: 'text-red-600 bg-red-50',
      occupant: 'Jane Smith, Mike <PERSON>'
    },
    {
      id: 4,
      roomNumber: 'B-202',
      type: 'Double Sharing',
      floor: '2nd Floor',
      capacity: 2,
      price: 10000,
      status: 'Partially Occupied',
      statusColor: 'text-yellow-600 bg-yellow-50',
      occupant: 'Alice Brown'
    },
    {
      id: 5,
      roomNumber: 'C-301',
      type: 'Triple Sharing',
      floor: '3rd Floor',
      capacity: 3,
      price: 8000,
      status: 'Available',
      statusColor: 'text-green-600 bg-green-50',
      occupant: null
    },
    {
      id: 6,
      roomNumber: 'D-101',
      type: 'Four Sharing',
      floor: '1st Floor',
      capacity: 4,
      price: 6000,
      status: 'Maintenance',
      statusColor: 'text-orange-600 bg-orange-50',
      occupant: null
    }
  ];

  const filteredRooms = rooms.filter(room => {
    const matchesSearch = room.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         room.type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || room.type === filterType;
    const matchesStatus = filterStatus === 'all' || room.status.toLowerCase() === filterStatus.toLowerCase();
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const roomTypes = ['Single Sharing', 'Double Sharing', 'Triple Sharing', 'Four Sharing'];
  const statusOptions = ['Available', 'Occupied', 'Partially Occupied', 'Maintenance'];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-purple-100 p-3 rounded-full">
              <Bed className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Rooms Management</h2>
              <p className="text-gray-600">Manage all rooms and their occupancy</p>
            </div>
          </div>
          <button
            onClick={() => setShowAddRoom(true)}
            className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors font-semibold flex items-center space-x-2"
          >
            <Plus className="h-5 w-5" />
            <span>Add Room</span>
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search rooms..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                <option value="all">All Types</option>
                {roomTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              {statusOptions.map(status => (
                <option key={status} value={status.toLowerCase()}>{status}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Room Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Rooms</p>
              <p className="text-2xl font-bold text-gray-900">{rooms.length}</p>
            </div>
            <div className="bg-gray-100 p-3 rounded-full">
              <Bed className="h-6 w-6 text-gray-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Available</p>
              <p className="text-2xl font-bold text-green-600">
                {rooms.filter(r => r.status === 'Available').length}
              </p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Occupied</p>
              <p className="text-2xl font-bold text-red-600">
                {rooms.filter(r => r.status === 'Occupied').length}
              </p>
            </div>
            <div className="bg-red-100 p-3 rounded-full">
              <XCircle className="h-6 w-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Maintenance</p>
              <p className="text-2xl font-bold text-orange-600">
                {rooms.filter(r => r.status === 'Maintenance').length}
              </p>
            </div>
            <div className="bg-orange-100 p-3 rounded-full">
              <Bed className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Rooms Table */}
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">All Rooms ({filteredRooms.length})</h3>
        </div>
        <div className="p-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-sm text-gray-600 border-b border-gray-200">
                  <th className="pb-3">Room Number</th>
                  <th className="pb-3">Type</th>
                  <th className="pb-3">Floor</th>
                  <th className="pb-3">Capacity</th>
                  <th className="pb-3">Price</th>
                  <th className="pb-3">Status</th>
                  <th className="pb-3">Occupant(s)</th>
                  <th className="pb-3">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredRooms.map((room) => (
                  <tr key={room.id} className="border-b border-gray-100">
                    <td className="py-4">
                      <div className="flex items-center space-x-2">
                        <Bed className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-gray-900">{room.roomNumber}</span>
                      </div>
                    </td>
                    <td className="py-4 text-gray-600">{room.type}</td>
                    <td className="py-4 text-gray-600">{room.floor}</td>
                    <td className="py-4">
                      <div className="flex items-center space-x-1">
                        <Users className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-900">{room.capacity}</span>
                      </div>
                    </td>
                    <td className="py-4 font-medium text-gray-900">₹{room.price.toLocaleString()}</td>
                    <td className="py-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${room.statusColor}`}>
                        {room.status}
                      </span>
                    </td>
                    <td className="py-4">
                      <span className="text-sm text-gray-600">
                        {room.occupant || 'None'}
                      </span>
                    </td>
                    <td className="py-4">
                      <div className="flex items-center space-x-2">
                        <button className="text-blue-600 hover:text-blue-800 p-1 rounded hover:bg-blue-100 transition-colors">
                          <Eye className="h-4 w-4" />
                        </button>
                        <button className="text-green-600 hover:text-green-800 p-1 rounded hover:bg-green-100 transition-colors">
                          <Edit3 className="h-4 w-4" />
                        </button>
                        <button className="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-100 transition-colors">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Add Room Modal */}
      {showAddRoom && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Add New Room</h3>
            <form className="space-y-4">
              <input
                type="text"
                placeholder="Room Number (e.g., A-105)"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                <option value="">Select Room Type</option>
                {roomTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
              <input
                type="text"
                placeholder="Floor (e.g., 1st Floor)"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <input
                type="number"
                placeholder="Monthly Rent"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setShowAddRoom(false)}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex-1 bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Add Room
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminRooms;