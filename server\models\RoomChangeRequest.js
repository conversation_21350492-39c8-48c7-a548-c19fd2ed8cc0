const mongoose = require('mongoose');

const roomChangeRequestSchema = new mongoose.Schema({
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Customer is required']
  },
  currentRoom: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Room',
    required: [true, 'Current room is required']
  },
  requestedRoom: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Room',
    required: [true, 'Requested room is required']
  },
  reason: {
    type: String,
    required: [true, 'Reason is required'],
    trim: true,
    maxlength: [500, 'Reason cannot exceed 500 characters']
  },
  status: {
    type: String,
    enum: ['Pending', 'Approved', 'Rejected', 'Completed'],
    default: 'Pending'
  },
  requestDate: {
    type: Date,
    default: Date.now
  },
  approvedDate: {
    type: Date
  },
  completedDate: {
    type: Date
  },
  adminNotes: {
    type: String,
    trim: true,
    maxlength: [500, 'Admin notes cannot exceed 500 characters']
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Approve request
roomChangeRequestSchema.methods.approve = function(adminId, notes) {
  this.status = 'Approved';
  this.approvedDate = new Date();
  this.approvedBy = adminId;
  if (notes) this.adminNotes = notes;
  return this.save();
};

// Reject request
roomChangeRequestSchema.methods.reject = function(adminId, notes) {
  this.status = 'Rejected';
  this.approvedBy = adminId;
  if (notes) this.adminNotes = notes;
  return this.save();
};

// Complete request
roomChangeRequestSchema.methods.complete = function() {
  this.status = 'Completed';
  this.completedDate = new Date();
  return this.save();
};

module.exports = mongoose.model('RoomChangeRequest', roomChangeRequestSchema);
