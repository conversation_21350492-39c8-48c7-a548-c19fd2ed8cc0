const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const Room = require('../models/Room');
const Payment = require('../models/Payment');
const MaintenanceRequest = require('../models/MaintenanceRequest');
const RoomChangeRequest = require('../models/RoomChangeRequest');
const { customerAuth } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/customer/profile
// @desc    Get customer profile
// @access  Customer only
router.get('/profile', customerAuth, async (req, res) => {
  try {
    const customer = await User.findById(req.user._id)
      .select('-password')
      .populate('roomNumber', 'roomNumber type price facilities');
    
    res.json(customer);
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/customer/profile
// @desc    Update customer profile
// @access  Customer only
router.put('/profile', [
  customerAuth,
  body('name').optional().trim().isLength({ min: 2 }),
  body('phone').optional().isMobilePhone(),
  body('location').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    const updateData = req.body;
    delete updateData.email; // Prevent email updates
    delete updateData.password; // Prevent password updates
    delete updateData.role; // Prevent role changes
    delete updateData.status; // Prevent status changes
    
    const updatedCustomer = await User.findByIdAndUpdate(
      req.user._id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');
    
    res.json({
      message: 'Profile updated successfully',
      customer: updatedCustomer
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/customer/payments
// @desc    Get customer payments
// @access  Customer only
router.get('/payments', customerAuth, async (req, res) => {
  try {
    const payments = await Payment.find({ customer: req.user._id })
      .populate('room', 'roomNumber type')
      .sort({ paymentDate: -1 });
    
    res.json(payments);
  } catch (error) {
    console.error('Get payments error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/customer/maintenance-requests
// @desc    Get customer maintenance requests
// @access  Customer only
router.get('/maintenance-requests', customerAuth, async (req, res) => {
  try {
    const requests = await MaintenanceRequest.find({ customer: req.user._id })
      .populate('room', 'roomNumber type')
      .sort({ requestDate: -1 });
    
    res.json(requests);
  } catch (error) {
    console.error('Get maintenance requests error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/customer/maintenance-requests
// @desc    Create maintenance request
// @access  Customer only
router.post('/maintenance-requests', [
  customerAuth,
  body('title').trim().isLength({ min: 5 }).withMessage('Title must be at least 5 characters'),
  body('description').trim().isLength({ min: 10 }).withMessage('Description must be at least 10 characters'),
  body('category').isIn(['Plumbing', 'Electrical', 'Cleaning', 'Furniture', 'AC/Heating', 'Internet', 'Other']),
  body('priority').optional().isIn(['Low', 'Medium', 'High', 'Urgent'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    // Get customer's room
    const customer = await User.findById(req.user._id);
    if (!customer.roomNumber) {
      return res.status(400).json({ message: 'No room assigned to create maintenance request' });
    }
    
    const request = new MaintenanceRequest({
      customer: req.user._id,
      room: customer.roomNumber,
      title: req.body.title,
      description: req.body.description,
      category: req.body.category,
      priority: req.body.priority || 'Medium'
    });
    
    await request.save();
    await request.populate('room', 'roomNumber type');
    
    res.status(201).json({
      message: 'Maintenance request created successfully',
      request
    });
  } catch (error) {
    console.error('Create maintenance request error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/customer/room-change-requests
// @desc    Get customer room change requests
// @access  Customer only
router.get('/room-change-requests', customerAuth, async (req, res) => {
  try {
    const requests = await RoomChangeRequest.find({ customer: req.user._id })
      .populate('currentRoom', 'roomNumber type price')
      .populate('requestedRoom', 'roomNumber type price')
      .sort({ requestDate: -1 });
    
    res.json(requests);
  } catch (error) {
    console.error('Get room change requests error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/customer/room-change-requests
// @desc    Create room change request
// @access  Customer only
router.post('/room-change-requests', [
  customerAuth,
  body('requestedRoom').isMongoId().withMessage('Valid room ID required'),
  body('reason').trim().isLength({ min: 10 }).withMessage('Reason must be at least 10 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    const customer = await User.findById(req.user._id);
    if (!customer.roomNumber) {
      return res.status(400).json({ message: 'No current room assigned' });
    }
    
    // Check if requested room is available
    const requestedRoom = await Room.findById(req.body.requestedRoom);
    if (!requestedRoom) {
      return res.status(404).json({ message: 'Requested room not found' });
    }
    
    if (!requestedRoom.isAvailable) {
      return res.status(400).json({ message: 'Requested room is not available' });
    }
    
    const request = new RoomChangeRequest({
      customer: req.user._id,
      currentRoom: customer.roomNumber,
      requestedRoom: req.body.requestedRoom,
      reason: req.body.reason
    });
    
    await request.save();
    await request.populate(['currentRoom', 'requestedRoom'], 'roomNumber type price');
    
    res.status(201).json({
      message: 'Room change request created successfully',
      request
    });
  } catch (error) {
    console.error('Create room change request error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
