import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  Download,
  Eye,
  Calendar,
  CheckCircle,
  AlertCircle,
  Clock,
  Filter
} from 'lucide-react';

interface Payment {
  _id: string;
  amount: number;
  finalAmount: number;
  paymentDate: string;
  dueDate: string;
  method: string;
  status: string;
  invoice: string;
  description: string;
  transactionId?: string;
  room: {
    roomNumber: string;
    type: string;
  };
}

const Payments: React.FC = () => {
  const [filterStatus, setFilterStatus] = useState('all');
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPayments();
  }, []);

  const fetchPayments = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/customer/payments', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPayments(data.payments || []);
      } else {
        console.error('Failed to fetch payments');
        setPayments([]);
      }
    } catch (error) {
      console.error('Error fetching payments:', error);
      setPayments([]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'failed':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const filteredPayments = filterStatus === 'all'
    ? payments
    : payments.filter(payment => payment.status.toLowerCase() === filterStatus.toLowerCase());

  const totalPayment = payments.reduce((sum, payment) => sum + payment.finalAmount, 0);
  const paidAmount = payments
    .filter(payment => payment.status.toLowerCase() === 'completed')
    .reduce((sum, payment) => sum + payment.finalAmount, 0);
  const pendingAmount = payments
    .filter(payment => payment.status.toLowerCase() === 'pending')
    .reduce((sum, payment) => sum + payment.finalAmount, 0);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading payments...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center space-x-3">
          <div className="bg-green-100 p-3 rounded-full">
            <CreditCard className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Payments</h2>
            <p className="text-gray-600">Manage your payment history and pending dues</p>
          </div>
        </div>
      </div>

      {/* Payment Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Payment</p>
              <p className="text-2xl font-bold text-gray-900">₹{totalPayment.toLocaleString()}</p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <CreditCard className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Paid Amount</p>
              <p className="text-2xl font-bold text-green-600">₹{paidAmount.toLocaleString()}</p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending Amount</p>
              <p className="text-2xl font-bold text-red-600">₹{pendingAmount.toLocaleString()}</p>
            </div>
            <div className="bg-red-100 p-3 rounded-full">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Payment History */}
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Payment History</h3>
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-gray-400" />
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="completed">Completed</option>
                  <option value="pending">Pending</option>
                  <option value="failed">Failed</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        <div className="p-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-sm text-gray-600 border-b border-gray-200">
                  <th className="pb-3">Payment Date</th>
                  <th className="pb-3">Booking</th>
                  <th className="pb-3">Amount</th>
                  <th className="pb-3">Status</th>
                  <th className="pb-3">Action</th>
                </tr>
              </thead>
              <tbody>
                {filteredPayments.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="py-8 text-center text-gray-500">
                      No payments found
                    </td>
                  </tr>
                ) : (
                  filteredPayments.map((payment) => (
                    <tr key={payment._id} className="border-b border-gray-100">
                      <td className="py-4">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <span className="text-gray-900">{new Date(payment.paymentDate).toLocaleDateString()}</span>
                        </div>
                      </td>
                      <td className="py-4">
                        <div>
                          <p className="font-medium text-gray-900">{payment.room.roomNumber} - {payment.description}</p>
                          <p className="text-sm text-gray-500">Method: {payment.method}</p>
                        </div>
                      </td>
                      <td className="py-4">
                        <span className="font-semibold text-gray-900">₹{payment.finalAmount.toLocaleString()}</span>
                      </td>
                      <td className="py-4">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(payment.status)} flex items-center space-x-1 w-fit`}>
                          {payment.status.toLowerCase() === 'completed' ? (
                            <CheckCircle className="h-3 w-3" />
                          ) : payment.status.toLowerCase() === 'pending' ? (
                            <Clock className="h-3 w-3" />
                          ) : (
                            <AlertCircle className="h-3 w-3" />
                          )}
                          <span>{payment.status}</span>
                        </span>
                      </td>
                      <td className="py-4">
                        <div className="flex items-center space-x-2">
                          <button className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1 bg-blue-50 px-2 py-1 rounded hover:bg-blue-100 transition-colors">
                            <Eye className="h-4 w-4" />
                            <span>View</span>
                          </button>
                          {payment.status.toLowerCase() === 'completed' && (
                            <button className="text-green-600 hover:text-green-800 text-sm font-medium flex items-center space-x-1 bg-green-50 px-2 py-1 rounded hover:bg-green-100 transition-colors">
                              <Download className="h-4 w-4" />
                              <span>Receipt</span>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Payment Actions */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Next Payment Due</h3>
            <p className="text-gray-600">February 2024 rent payment</p>
            <p className="text-2xl font-bold text-blue-600 mt-2">₹12,000</p>
          </div>
          <div className="flex flex-col space-y-2">
            <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-semibold">
              Pay Now
            </button>
            <button className="bg-white text-blue-600 border border-blue-600 px-6 py-2 rounded-lg hover:bg-blue-50 transition-colors font-semibold">
              Set Auto-Pay
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Payments;