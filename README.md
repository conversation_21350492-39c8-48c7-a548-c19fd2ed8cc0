# PG Management System

A comprehensive web application for managing Paying Guest (PG) accommodations with separate interfaces for administrators and customers.

## Features

### Admin Features
- Dashboard with statistics and analytics
- Customer management (view, edit, delete customers)
- Room management (add, edit, assign rooms)
- Payment tracking and management
- Maintenance request handling
- Room change request approval
- Generate monthly payments

### Customer Features
- Personal dashboard
- View room details and facilities
- Payment history and status
- Submit maintenance requests
- Request room changes
- Profile management
- Notifications

## Technology Stack

### Frontend
- React 18.3.1 with TypeScript
- Vite (build tool)
- Tailwind CSS (styling)
- React Router DOM (routing)
- Lucide React (icons)
- React Hook Form (forms)

### Backend
- Node.js with Express.js
- MongoDB with Mongoose
- JWT authentication
- bcryptjs (password hashing)
- Express Validator (input validation)

## Prerequisites

- Node.js (v16 or higher)
- MongoDB (running on localhost:27017)
- MongoDB Compass (optional, for database management)

## Installation & Setup

### 1. Clone the repository
```bash
git clone <repository-url>
cd pg-management-system
```

### 2. Backend Setup
```bash
cd server
npm install
```

Create a `.env` file in the server directory:
```env
PORT=5000
MONGODB_URI=mongodb://localhost:27017/pg_management
JWT_SECRET=your_jwt_secret_key_here_change_in_production
NODE_ENV=development
```

### 3. Frontend Setup
```bash
cd project
npm install
```

### 4. Database Setup

Make sure MongoDB is running on your system, then seed the database:
```bash
cd server
npm run seed
```

This will create:
- Admin user: `<EMAIL>` / `admin123`
- Sample customers with credentials: `customer123`
- 20 rooms across different categories
- Sample data for testing

### 5. Running the Application

#### Start the backend server:
```bash
cd server
npm run dev
```
The server will run on http://localhost:5000

#### Start the frontend development server:
```bash
cd project
npm run dev
```
The frontend will run on http://localhost:5173

## Login Credentials

### Admin Access
- Email: `<EMAIL>`
- Password: `admin123`

### Customer Access
- Email: `<EMAIL>`
- Password: `customer123`

(Additional customer accounts available - check console output after seeding)

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new customer
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - Logout user

### Admin Routes
- `GET /api/admin/customers` - Get all customers
- `GET /api/admin/customers/:id` - Get customer by ID
- `PUT /api/admin/customers/:id` - Update customer
- `DELETE /api/admin/customers/:id` - Delete customer
- `GET /api/admin/dashboard/stats` - Get dashboard statistics

### Customer Routes
- `GET /api/customer/profile` - Get customer profile
- `PUT /api/customer/profile` - Update customer profile
- `GET /api/customer/payments` - Get customer payments
- `GET /api/customer/maintenance-requests` - Get maintenance requests
- `POST /api/customer/maintenance-requests` - Create maintenance request

### Rooms
- `GET /api/rooms` - Get all rooms
- `POST /api/rooms` - Create new room (admin only)
- `PUT /api/rooms/:id` - Update room (admin only)
- `DELETE /api/rooms/:id` - Delete room (admin only)
- `POST /api/rooms/:id/assign` - Assign customer to room (admin only)

### Payments
- `GET /api/payments` - Get payments
- `POST /api/payments` - Create payment (admin only)
- `PUT /api/payments/:id` - Update payment (admin only)
- `POST /api/payments/generate-monthly` - Generate monthly payments (admin only)

## Database Schema

### User Model
- Personal information (name, email, phone, location)
- Authentication (password, role)
- Room assignment (roomNumber, rent)
- Status tracking (Active, Pending, Inactive)

### Room Model
- Room details (number, type, floor, capacity, price)
- Occupancy tracking (occupants array)
- Status management (Available, Occupied, Partially Occupied, Maintenance)
- Facilities and amenities

### Payment Model
- Payment tracking (amount, date, method, status)
- Invoice generation
- Late fees and discounts
- Transaction details

### Maintenance Request Model
- Request details (title, description, category, priority)
- Status tracking (Pending, In Progress, Completed, Cancelled)
- Cost estimation and tracking

### Room Change Request Model
- Request tracking (current room, requested room, reason)
- Approval workflow (Pending, Approved, Rejected, Completed)
- Admin notes and approval tracking

## Development

### Adding New Features
1. Create/update database models in `server/models/`
2. Add API routes in `server/routes/`
3. Update frontend components in `project/src/components/`
4. Add new pages in `project/src/pages/`

### Environment Variables
- `PORT` - Server port (default: 5000)
- `MONGODB_URI` - MongoDB connection string
- `JWT_SECRET` - Secret key for JWT tokens
- `NODE_ENV` - Environment (development/production)

## Production Deployment

1. Set environment variables for production
2. Build the frontend: `npm run build`
3. Configure reverse proxy (nginx) to serve frontend and proxy API calls
4. Use PM2 or similar for process management
5. Set up MongoDB with proper authentication
6. Configure SSL certificates

## Troubleshooting

### Common Issues
1. **MongoDB Connection Error**: Ensure MongoDB is running on localhost:27017
2. **Port Already in Use**: Change PORT in .env file
3. **CORS Issues**: Check proxy configuration in vite.config.ts
4. **Authentication Errors**: Verify JWT_SECRET is set correctly

### Database Reset
To reset the database and reseed:
```bash
cd server
npm run seed
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the ISC License.
