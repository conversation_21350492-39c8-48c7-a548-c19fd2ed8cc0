import React, { useState } from 'react';
import { 
  Wrench, 
  Plus, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Send,
  Eye
} from 'lucide-react';

const MaintenanceRequests: React.FC = () => {
  const [showNewRequest, setShowNewRequest] = useState(false);
  const [requestType, setRequestType] = useState('');
  const [priority, setPriority] = useState('');
  const [description, setDescription] = useState('');

  const requestTypes = [
    'Electrical',
    'Plumbing',
    'AC/Heating',
    'Furniture',
    'Cleaning',
    'Internet/WiFi',
    'Other'
  ];

  const priorities = [
    { value: 'low', label: 'Low', color: 'text-green-600 bg-green-50' },
    { value: 'medium', label: 'Medium', color: 'text-yellow-600 bg-yellow-50' },
    { value: 'high', label: 'High', color: 'text-orange-600 bg-orange-50' },
    { value: 'urgent', label: 'Urgent', color: 'text-red-600 bg-red-50' }
  ];

  const requestHistory = [
    {
      id: 1,
      requestType: 'Plumbing',
      priority: 'high',
      description: 'Water leakage in bathroom tap',
      status: 'In Progress',
      statusColor: 'text-blue-600 bg-blue-50',
      requestDate: '2024-01-25',
      completionDate: null
    },
    {
      id: 2,
      requestType: 'Electrical',
      priority: 'medium',
      description: 'Light bulb replacement needed',
      status: 'Completed',
      statusColor: 'text-green-600 bg-green-50',
      requestDate: '2024-01-20',
      completionDate: '2024-01-22'
    },
    {
      id: 3,
      requestType: 'AC/Heating',
      priority: 'urgent',
      description: 'AC not working properly',
      status: 'Pending',
      statusColor: 'text-yellow-600 bg-yellow-50',
      requestDate: '2024-01-28',
      completionDate: null
    },
    {
      id: 4,
      requestType: 'Internet/WiFi',
      priority: 'low',
      description: 'Slow internet connection',
      status: 'Rejected',
      statusColor: 'text-red-600 bg-red-50',
      requestDate: '2024-01-15',
      completionDate: null
    }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!requestType || !priority || !description.trim()) {
      alert('Please fill in all fields');
      return;
    }
    alert('Maintenance request submitted successfully!');
    setRequestType('');
    setPriority('');
    setDescription('');
    setShowNewRequest(false);
  };

  const getPriorityColor = (priority: string) => {
    const priorityObj = priorities.find(p => p.value === priority);
    return priorityObj?.color || 'text-gray-600 bg-gray-50';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-orange-100 p-3 rounded-full">
              <Wrench className="h-6 w-6 text-orange-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Maintenance Requests</h2>
              <p className="text-gray-600">Submit and track your maintenance requests</p>
            </div>
          </div>
          <button
            onClick={() => setShowNewRequest(!showNewRequest)}
            className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors font-semibold flex items-center space-x-2"
          >
            <Plus className="h-5 w-5" />
            <span>New Request</span>
          </button>
        </div>
      </div>

      {/* New Request Form */}
      {showNewRequest && (
        <div className="bg-white rounded-xl shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">New Maintenance Request</h3>
          </div>
          <div className="p-6">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Request Type
                  </label>
                  <select
                    value={requestType}
                    onChange={(e) => setRequestType(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    required
                  >
                    <option value="">Select request type...</option>
                    {requestTypes.map((type) => (
                      <option key={type} value={type}>
                        {type}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Priority
                  </label>
                  <select
                    value={priority}
                    onChange={(e) => setPriority(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    required
                  >
                    <option value="">Select priority...</option>
                    {priorities.map((p) => (
                      <option key={p.value} value={p.value}>
                        {p.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Please describe the maintenance issue in detail..."
                  required
                />
              </div>

              <div className="flex items-center space-x-3">
                <button
                  type="submit"
                  className="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors font-semibold flex items-center space-x-2"
                >
                  <Send className="h-4 w-4" />
                  <span>Submit Request</span>
                </button>
                <button
                  type="button"
                  onClick={() => setShowNewRequest(false)}
                  className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors font-semibold"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Request Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Requests</p>
              <p className="text-2xl font-bold text-gray-900">{requestHistory.length}</p>
            </div>
            <div className="bg-gray-100 p-3 rounded-full">
              <Wrench className="h-6 w-6 text-gray-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">
                {requestHistory.filter(r => r.status === 'Pending').length}
              </p>
            </div>
            <div className="bg-yellow-100 p-3 rounded-full">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">In Progress</p>
              <p className="text-2xl font-bold text-blue-600">
                {requestHistory.filter(r => r.status === 'In Progress').length}
              </p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <AlertTriangle className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">
                {requestHistory.filter(r => r.status === 'Completed').length}
              </p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Request History */}
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Request History</h3>
        </div>
        <div className="p-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-sm text-gray-600 border-b border-gray-200">
                  <th className="pb-3">Request Type</th>
                  <th className="pb-3">Priority</th>
                  <th className="pb-3">Description</th>
                  <th className="pb-3">Status</th>
                  <th className="pb-3">Request Date</th>
                  <th className="pb-3">Action</th>
                </tr>
              </thead>
              <tbody>
                {requestHistory.map((request) => (
                  <tr key={request.id} className="border-b border-gray-100">
                    <td className="py-4">
                      <div className="flex items-center space-x-2">
                        <Wrench className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-gray-900">{request.requestType}</span>
                      </div>
                    </td>
                    <td className="py-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(request.priority)}`}>
                        {priorities.find(p => p.value === request.priority)?.label}
                      </span>
                    </td>
                    <td className="py-4">
                      <p className="text-sm text-gray-900 max-w-xs truncate">
                        {request.description}
                      </p>
                    </td>
                    <td className="py-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${request.statusColor} flex items-center space-x-1 w-fit`}>
                        {request.status === 'Completed' ? (
                          <CheckCircle className="h-3 w-3" />
                        ) : request.status === 'In Progress' ? (
                          <AlertTriangle className="h-3 w-3" />
                        ) : (
                          <Clock className="h-3 w-3" />
                        )}
                        <span>{request.status}</span>
                      </span>
                    </td>
                    <td className="py-4 text-sm text-gray-600">
                      {request.requestDate}
                    </td>
                    <td className="py-4">
                      <button className="text-orange-600 hover:text-orange-800 text-sm font-medium flex items-center space-x-1 bg-orange-50 px-2 py-1 rounded hover:bg-orange-100 transition-colors">
                        <Eye className="h-4 w-4" />
                        <span>View</span>
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MaintenanceRequests;