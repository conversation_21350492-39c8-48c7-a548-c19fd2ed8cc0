const mongoose = require('mongoose');

const maintenanceRequestSchema = new mongoose.Schema({
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Customer is required']
  },
  room: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Room',
    required: [true, 'Room is required']
  },
  title: {
    type: String,
    required: [true, 'Request title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: ['Plumbing', 'Electrical', 'Cleaning', 'Furniture', 'AC/Heating', 'Internet', 'Other']
  },
  priority: {
    type: String,
    enum: ['Low', 'Medium', 'High', 'Urgent'],
    default: 'Medium'
  },
  status: {
    type: String,
    enum: ['Pending', 'In Progress', 'Completed', 'Cancelled'],
    default: 'Pending'
  },
  requestDate: {
    type: Date,
    default: Date.now
  },
  assignedTo: {
    type: String,
    trim: true
  },
  completedDate: {
    type: Date
  },
  estimatedCost: {
    type: Number,
    min: [0, 'Cost cannot be negative']
  },
  actualCost: {
    type: Number,
    min: [0, 'Cost cannot be negative']
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Notes cannot exceed 1000 characters']
  },
  images: [{
    type: String,
    trim: true
  }]
}, {
  timestamps: true
});

// Mark as completed
maintenanceRequestSchema.methods.markCompleted = function(actualCost, notes) {
  this.status = 'Completed';
  this.completedDate = new Date();
  if (actualCost !== undefined) this.actualCost = actualCost;
  if (notes) this.notes = notes;
  return this.save();
};

module.exports = mongoose.model('MaintenanceRequest', maintenanceRequestSchema);
