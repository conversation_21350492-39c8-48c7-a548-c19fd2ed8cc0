import React from 'react';
import { 
  Users, 
  Bed, 
  CreditCard, 
  CheckCircle, 
  AlertCircle,
  Eye,
  TrendingUp,
  Calendar
} from 'lucide-react';

const AdminDashboardHome: React.FC = () => {
  const stats = {
    totalCustomers: 45,
    totalRooms: 50,
    availableRooms: 5,
    totalBookings: 45,
    monthlyRevenue: 540000,
    pendingPayments: 3
  };

  const availableRooms = [
    { roomNumber: 'A-105', type: 'Single Sharing', status: 'Available', price: 12000 },
    { roomNumber: 'B-203', type: 'Double Sharing', status: 'Available', price: 10000 },
    { roomNumber: 'C-304', type: 'Triple Sharing', status: 'Available', price: 8000 },
    { roomNumber: 'D-105', type: 'Four Sharing', status: 'Available', price: 6000 },
    { roomNumber: 'A-108', type: 'Single Sharing', status: 'Available', price: 12000 }
  ];

  const recentCustomers = [
    {
      id: 1,
      customer: '<PERSON>',
      room: 'A-101',
      checkInDate: '2024-01-15',
      status: 'Active',
      statusColor: 'text-green-600 bg-green-50'
    },
    {
      id: 2,
      customer: '<PERSON> Smith',
      room: 'B-201',
      checkInDate: '2024-01-20',
      status: 'Active',
      statusColor: 'text-green-600 bg-green-50'
    },
    {
      id: 3,
      customer: 'Mike Johnson',
      room: 'C-301',
      checkInDate: '2024-01-25',
      status: 'Pending',
      statusColor: 'text-yellow-600 bg-yellow-50'
    }
  ];

  const paymentStatus = [
    {
      id: 1,
      invoice: 'INV-001',
      customer: 'John Doe',
      amount: 12000,
      date: '2024-02-01',
      status: 'Paid',
      statusColor: 'text-green-600 bg-green-50'
    },
    {
      id: 2,
      invoice: 'INV-002',
      customer: 'Jane Smith',
      amount: 10000,
      date: '2024-02-01',
      status: 'Pending',
      statusColor: 'text-yellow-600 bg-yellow-50'
    },
    {
      id: 3,
      invoice: 'INV-003',
      customer: 'Mike Johnson',
      amount: 8000,
      date: '2024-02-01',
      status: 'Overdue',
      statusColor: 'text-red-600 bg-red-50'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-xl text-white p-6">
        <h2 className="text-2xl font-bold mb-2">Welcome to Admin Dashboard</h2>
        <p className="text-gray-300">Manage your PG operations efficiently</p>
        <div className="mt-4 flex items-center space-x-6">
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Today: {new Date().toLocaleDateString()}</span>
          </div>
          <div className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Revenue: ₹{stats.monthlyRevenue.toLocaleString()}</span>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Customers</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalCustomers}</p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Rooms</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalRooms}</p>
            </div>
            <div className="bg-purple-100 p-3 rounded-full">
              <Bed className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Available Rooms</p>
              <p className="text-2xl font-bold text-green-600">{stats.availableRooms}</p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Bookings</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalBookings}</p>
            </div>
            <div className="bg-orange-100 p-3 rounded-full">
              <CreditCard className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Available Rooms */}
        <div className="bg-white rounded-xl shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Available Rooms</h3>
          </div>
          <div className="p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left text-sm text-gray-600">
                    <th className="pb-3">Room Number</th>
                    <th className="pb-3">Type</th>
                    <th className="pb-3">Status</th>
                    <th className="pb-3">Price</th>
                    <th className="pb-3">Action</th>
                  </tr>
                </thead>
                <tbody className="space-y-2">
                  {availableRooms.map((room, index) => (
                    <tr key={index} className="border-t border-gray-100">
                      <td className="py-3 font-medium">{room.roomNumber}</td>
                      <td className="py-3 text-gray-600">{room.type}</td>
                      <td className="py-3">
                        <span className="px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-50">
                          {room.status}
                        </span>
                      </td>
                      <td className="py-3 font-medium">₹{room.price.toLocaleString()}</td>
                      <td className="py-3">
                        <button className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1">
                          <Eye className="h-4 w-4" />
                          <span>View</span>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Recent Customers */}
        <div className="bg-white rounded-xl shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Customers</h3>
          </div>
          <div className="p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left text-sm text-gray-600">
                    <th className="pb-3">Customer</th>
                    <th className="pb-3">Room</th>
                    <th className="pb-3">Check-in Date</th>
                    <th className="pb-3">Status</th>
                    <th className="pb-3">Action</th>
                  </tr>
                </thead>
                <tbody className="space-y-2">
                  {recentCustomers.map((customer) => (
                    <tr key={customer.id} className="border-t border-gray-100">
                      <td className="py-3 font-medium">{customer.customer}</td>
                      <td className="py-3 text-gray-600">{customer.room}</td>
                      <td className="py-3 text-gray-600">{customer.checkInDate}</td>
                      <td className="py-3">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${customer.statusColor}`}>
                          {customer.status}
                        </span>
                      </td>
                      <td className="py-3">
                        <button className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1">
                          <Eye className="h-4 w-4" />
                          <span>View</span>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Status */}
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Payment Status</h3>
        </div>
        <div className="p-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-sm text-gray-600 border-b border-gray-200">
                  <th className="pb-3">Invoice</th>
                  <th className="pb-3">Customer</th>
                  <th className="pb-3">Amount</th>
                  <th className="pb-3">Date</th>
                  <th className="pb-3">Status</th>
                  <th className="pb-3">Action</th>
                </tr>
              </thead>
              <tbody>
                {paymentStatus.map((payment) => (
                  <tr key={payment.id} className="border-b border-gray-100">
                    <td className="py-4 font-medium">{payment.invoice}</td>
                    <td className="py-4">{payment.customer}</td>
                    <td className="py-4 font-medium">₹{payment.amount.toLocaleString()}</td>
                    <td className="py-4 text-gray-600">{payment.date}</td>
                    <td className="py-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${payment.statusColor} flex items-center space-x-1 w-fit`}>
                        {payment.status === 'Paid' ? (
                          <CheckCircle className="h-3 w-3" />
                        ) : (
                          <AlertCircle className="h-3 w-3" />
                        )}
                        <span>{payment.status}</span>
                      </span>
                    </td>
                    <td className="py-4">
                      <button className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1 bg-blue-50 px-2 py-1 rounded hover:bg-blue-100 transition-colors">
                        <Eye className="h-4 w-4" />
                        <span>View</span>
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboardHome;