import React, { useState, useEffect } from 'react';
import {
  Users,
  Bed,
  CreditCard,
  CheckCircle,
  AlertCircle,
  Eye,
  TrendingUp,
  Calendar
} from 'lucide-react';

interface DashboardStats {
  customers: {
    total: number;
    active: number;
    pending: number;
  };
  rooms: {
    total: number;
    available: number;
    occupied: number;
  };
  payments: {
    total: number;
    pending: number;
    overdue: number;
  };
  monthlyRevenue: number;
}

interface Room {
  _id: string;
  roomNumber: string;
  type: string;
  status: string;
  price: number;
}

interface Customer {
  _id: string;
  name: string;
  email: string;
  roomNumber?: any;
  status: string;
  registeredOn: string;
}

interface Payment {
  _id: string;
  invoice: string;
  customer: {
    name: string;
  };
  amount: number;
  paymentDate: string;
  status: string;
}

const AdminDashboardHome: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [availableRooms, setAvailableRooms] = useState<Room[]>([]);
  const [recentCustomers, setRecentCustomers] = useState<Customer[]>([]);
  const [recentPayments, setRecentPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('No authentication token found');
        return;
      }

      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      };

      // Fetch dashboard statistics
      const statsResponse = await fetch('/api/admin/dashboard/stats', { headers });
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }

      // Fetch available rooms
      const roomsResponse = await fetch('/api/rooms?available=true&limit=5', { headers });
      if (roomsResponse.ok) {
        const roomsData = await roomsResponse.json();
        setAvailableRooms(roomsData.slice(0, 5));
      }

      // Fetch recent customers
      const customersResponse = await fetch('/api/admin/customers?limit=5', { headers });
      if (customersResponse.ok) {
        const customersData = await customersResponse.json();
        setRecentCustomers(customersData.customers || []);
      }

      // Fetch recent payments
      const paymentsResponse = await fetch('/api/payments?limit=5', { headers });
      if (paymentsResponse.ok) {
        const paymentsData = await paymentsResponse.json();
        setRecentPayments(paymentsData.payments || []);
      }

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'completed':
      case 'available':
        return 'text-green-600 bg-green-50';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'inactive':
      case 'failed':
      case 'overdue':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading dashboard...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-red-600">{error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-xl text-white p-6">
        <h2 className="text-2xl font-bold mb-2">Welcome to Admin Dashboard</h2>
        <p className="text-gray-300">Manage your PG operations efficiently</p>
        <div className="mt-4 flex items-center space-x-6">
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Today: {new Date().toLocaleDateString()}</span>
          </div>
          <div className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Revenue: ₹{(stats?.monthlyRevenue || 0).toLocaleString()}</span>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Customers</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.customers.total || 0}</p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Rooms</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.rooms.total || 0}</p>
            </div>
            <div className="bg-purple-100 p-3 rounded-full">
              <Bed className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Available Rooms</p>
              <p className="text-2xl font-bold text-green-600">{stats?.rooms.available || 0}</p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Monthly Revenue</p>
              <p className="text-2xl font-bold text-gray-900">₹{(stats?.monthlyRevenue || 0).toLocaleString()}</p>
            </div>
            <div className="bg-orange-100 p-3 rounded-full">
              <CreditCard className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Available Rooms */}
        <div className="bg-white rounded-xl shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Available Rooms</h3>
          </div>
          <div className="p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left text-sm text-gray-600">
                    <th className="pb-3">Room Number</th>
                    <th className="pb-3">Type</th>
                    <th className="pb-3">Status</th>
                    <th className="pb-3">Price</th>
                    <th className="pb-3">Action</th>
                  </tr>
                </thead>
                <tbody className="space-y-2">
                  {availableRooms.length === 0 ? (
                    <tr>
                      <td colSpan={5} className="py-8 text-center text-gray-500">
                        No available rooms
                      </td>
                    </tr>
                  ) : (
                    availableRooms.map((room) => (
                      <tr key={room._id} className="border-t border-gray-100">
                        <td className="py-3 font-medium">{room.roomNumber}</td>
                        <td className="py-3 text-gray-600">{room.type}</td>
                        <td className="py-3">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(room.status)}`}>
                            {room.status}
                          </span>
                        </td>
                        <td className="py-3 font-medium">₹{room.price.toLocaleString()}</td>
                        <td className="py-3">
                          <button className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1">
                            <Eye className="h-4 w-4" />
                            <span>View</span>
                          </button>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Recent Customers */}
        <div className="bg-white rounded-xl shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Customers</h3>
          </div>
          <div className="p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left text-sm text-gray-600">
                    <th className="pb-3">Customer</th>
                    <th className="pb-3">Room</th>
                    <th className="pb-3">Check-in Date</th>
                    <th className="pb-3">Status</th>
                    <th className="pb-3">Action</th>
                  </tr>
                </thead>
                <tbody className="space-y-2">
                  {recentCustomers.length === 0 ? (
                    <tr>
                      <td colSpan={5} className="py-8 text-center text-gray-500">
                        No recent customers
                      </td>
                    </tr>
                  ) : (
                    recentCustomers.map((customer) => (
                      <tr key={customer._id} className="border-t border-gray-100">
                        <td className="py-3 font-medium">{customer.name}</td>
                        <td className="py-3 text-gray-600">{customer.roomNumber?.roomNumber || 'Not assigned'}</td>
                        <td className="py-3 text-gray-600">{new Date(customer.registeredOn).toLocaleDateString()}</td>
                        <td className="py-3">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(customer.status)}`}>
                            {customer.status}
                          </span>
                        </td>
                        <td className="py-3">
                          <button className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1">
                            <Eye className="h-4 w-4" />
                            <span>View</span>
                          </button>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Status */}
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Payment Status</h3>
        </div>
        <div className="p-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-sm text-gray-600 border-b border-gray-200">
                  <th className="pb-3">Invoice</th>
                  <th className="pb-3">Customer</th>
                  <th className="pb-3">Amount</th>
                  <th className="pb-3">Date</th>
                  <th className="pb-3">Status</th>
                  <th className="pb-3">Action</th>
                </tr>
              </thead>
              <tbody>
                {recentPayments.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="py-8 text-center text-gray-500">
                      No recent payments
                    </td>
                  </tr>
                ) : (
                  recentPayments.map((payment) => (
                    <tr key={payment._id} className="border-b border-gray-100">
                      <td className="py-4 font-medium">{payment.invoice}</td>
                      <td className="py-4">{payment.customer.name}</td>
                      <td className="py-4 font-medium">₹{payment.amount.toLocaleString()}</td>
                      <td className="py-4 text-gray-600">{new Date(payment.paymentDate).toLocaleDateString()}</td>
                      <td className="py-4">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(payment.status)} flex items-center space-x-1 w-fit`}>
                          {payment.status === 'Completed' ? (
                            <CheckCircle className="h-3 w-3" />
                          ) : (
                            <AlertCircle className="h-3 w-3" />
                          )}
                          <span>{payment.status}</span>
                        </span>
                      </td>
                      <td className="py-4">
                        <button className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1 bg-blue-50 px-2 py-1 rounded hover:bg-blue-100 transition-colors">
                          <Eye className="h-4 w-4" />
                          <span>View</span>
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboardHome;